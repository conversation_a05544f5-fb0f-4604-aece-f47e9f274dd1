<?php
// 添加錯誤處理與日誌記錄
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__FILE__) . '/../error_log.txt');
set_time_limit(120); // 增加腳本執行時間限制

// 確保用戶已登入
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Location: ../index.php');
    exit;
}

// 引入數據庫連接
require_once '../db_service.php';

// 初始化服務
$db = DBService::getInstance();

// 用戶資訊
$user_id = $_SESSION['user_id'];
$role = $_SESSION['role'];
$username = $_SESSION['username'] ?? '';
$site = $_SESSION['site'] ?? '';

// 獲取用戶資訊
function getUserData($db, $user_id, &$session) {
    if (!isset($session['name']) || empty($session['name']) || !isset($session['position_name'])) {
        $userData = $db->fetchOne(
            "SELECT u.name, u.site, u.signature_path, u.position_id, p.name as position_name
             FROM users u
             LEFT JOIN positions p ON u.position_id = p.id
             WHERE u.id = ?",
            [$user_id]
        );

        if ($userData) {
            $session['name'] = $userData['name'];
            $session['site'] = $userData['site'];
            $session['signature_path'] = $userData['signature_path'];
            $session['position_id'] = $userData['position_id'];
            $session['position_name'] = $userData['position_name'];
            return $userData['name'];
        }
        return '未知姓名';
    }
    return $session['name'];
}

// 獲取使用者資訊
$fullname = getUserData($db, $user_id, $_SESSION);
$site = $_SESSION['site'];

// Email通知函數
function sendApprovalNotificationEmail($approver_data, $requester_name, $site_name, $work_date, $request_id) {
    $to = $approver_data['email'];
    $approver_name = $approver_data['name'];

    $subject = "【人力需求審核通知】{$site_name} - {$work_date}";

    $message = "
    <html>
    <head>
        <meta charset='UTF-8'>
        <style>
            body { font-family: 'Microsoft JhengHei', Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4361EE; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background-color: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
            .info-box { background-color: white; padding: 15px; margin: 15px 0; border-left: 4px solid #4361EE; }
            .button { display: inline-block; background-color: #4361EE; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>人力需求審核通知</h2>
                <p>良有營造股份有限公司電子簽核系統</p>
            </div>
            <div class='content'>
                <p>親愛的 {$approver_name} 主管，您好：</p>

                <p>您有一筆新的人力需求申請需要審核，詳細資訊如下：</p>

                <div class='info-box'>
                    <strong>申請資訊：</strong><br>
                    • 申請人：{$requester_name}<br>
                    • 工地：{$site_name}<br>
                    • 工作日期：{$work_date}<br>
                    • 申請編號：#{$request_id}<br>
                    • 申請時間：" . date('Y-m-d H:i:s') . "
                </div>

                <p>請點擊下方按鈕進入系統進行審核：</p>

                <a href='" . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "://{$_SERVER['HTTP_HOST']}/manpower-requests/view_request.php?id={$request_id}' class='button'>
                    立即審核
                </a>

                <p><strong>注意事項：</strong></p>
                <ul>
                    <li>請儘快完成審核，以確保工地作業順利進行</li>
                    <li>如有疑問，請直接聯繫申請人</li>
                    <li>此為系統自動發送的通知郵件，請勿直接回覆</li>
                </ul>
            </div>
            <div class='footer'>
                <p>良有營造股份有限公司電子簽核系統</p>
                <p>系統時間：" . date('Y-m-d H:i:s') . "</p>
            </div>
        </div>
    </body>
    </html>
    ";

    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: 良有營造電子簽核系統 <<EMAIL>>" . "\r\n";
    $headers .= "Reply-To: <EMAIL>" . "\r\n";

    if (!mail($to, $subject, $message, $headers)) {
        throw new Exception("無法發送email通知到 {$to}");
    }
}

// 自動核可通知函數
function sendAutoApprovalNotificationEmail($approver_data, $requester_name, $site_name, $work_date, $request_id) {
    global $db;

    $to = $approver_data['email'];
    $approver_name = $approver_data['name'];

    // 獲取工作項目詳細資訊
    $work_items = $db->fetchAll(
        "SELECT work_description, workers_required, company_preference FROM manpower_request_items WHERE request_id = ? ORDER BY id",
        [$request_id]
    );

    // 計算總需求人數
    $total_workers = 0;
    foreach ($work_items as $item) {
        $total_workers += $item['workers_required'];
    }

    $subject = "【人力需求自動核可通知】{$site_name} - {$work_date}";

    // 建立工作項目HTML
    $work_items_html = "";
    $item_number = 1;
    foreach ($work_items as $item) {
        $company_text = !empty($item['company_preference']) ? $item['company_preference'] : '無指定';
        $work_items_html .= "
        <tr>
            <td style='padding: 12px; border: 1px solid #ddd; text-align: center; background-color: #f8f9fa;'>{$item_number}</td>
            <td style='padding: 12px; border: 1px solid #ddd;'>{$item['work_description']}</td>
            <td style='padding: 12px; border: 1px solid #ddd; text-align: center; font-weight: bold; color: #e74c3c;'>{$item['workers_required']} 人</td>
            <td style='padding: 12px; border: 1px solid #ddd; text-align: center;'>{$company_text}</td>
        </tr>";
        $item_number++;
    }

    $message = "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <style>
            body {
                font-family: 'Microsoft JhengHei', 'PingFang TC', Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
                background-color: #f4f4f4;
            }
            .email-container {
                max-width: 700px;
                margin: 20px auto;
                background-color: #ffffff;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            }
            .header {
                background: linear-gradient(135deg, #27ae60, #2ecc71);
                color: white;
                padding: 30px 20px;
                text-align: center;
                position: relative;
            }
            .header h1 {
                margin: 0 0 10px 0;
                font-size: 24px;
                font-weight: bold;
            }
            .header p {
                margin: 0;
                font-size: 14px;
                opacity: 0.9;
            }
            .approved-badge {
                background-color: rgba(255,255,255,0.2);
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: bold;
                margin-top: 15px;
                display: inline-block;
                border: 2px solid rgba(255,255,255,0.3);
            }
            .content {
                padding: 30px;
                background-color: #ffffff;
            }
            .greeting {
                font-size: 16px;
                margin-bottom: 20px;
                color: #2c3e50;
            }
            .info-section {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-left: 5px solid #27ae60;
                padding: 20px;
                margin: 20px 0;
                border-radius: 0 8px 8px 0;
            }
            .info-title {
                font-size: 18px;
                font-weight: bold;
                color: #27ae60;
                margin-bottom: 15px;
                display: flex;
                align-items: center;
            }
            .info-title::before {
                content: '📋';
                margin-right: 8px;
                font-size: 20px;
            }
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
                margin-bottom: 15px;
            }
            .info-item {
                background-color: white;
                padding: 12px;
                border-radius: 6px;
                border: 1px solid #e0e0e0;
            }
            .info-label {
                font-weight: bold;
                color: #555;
                font-size: 12px;
                text-transform: uppercase;
                margin-bottom: 5px;
            }
            .info-value {
                color: #2c3e50;
                font-size: 14px;
            }
            .status-approved {
                color: #27ae60;
                font-weight: bold;
                font-size: 16px;
            }
            .work-details {
                background-color: #ffffff;
                border: 2px solid #27ae60;
                border-radius: 8px;
                margin: 25px 0;
                overflow: hidden;
            }
            .work-header {
                background-color: #27ae60;
                color: white;
                padding: 15px 20px;
                font-size: 18px;
                font-weight: bold;
                display: flex;
                align-items: center;
            }
            .work-header::before {
                content: '🔧';
                margin-right: 10px;
                font-size: 20px;
            }
            .work-table {
                width: 100%;
                border-collapse: collapse;
                margin: 0;
            }
            .work-table th {
                background-color: #34495e;
                color: white;
                padding: 15px 12px;
                text-align: center;
                font-weight: bold;
                font-size: 14px;
            }
            .work-table td {
                padding: 12px;
                border: 1px solid #ddd;
                font-size: 14px;
            }
            .work-table tr:nth-child(even) {
                background-color: #f8f9fa;
            }
            .total-summary {
                background: linear-gradient(135deg, #3498db, #2980b9);
                color: white;
                padding: 15px 20px;
                text-align: center;
                font-size: 16px;
                font-weight: bold;
            }
            .action-button {
                display: inline-block;
                background: linear-gradient(135deg, #27ae60, #2ecc71);
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 25px;
                font-weight: bold;
                margin: 20px 0;
                text-align: center;
                box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
                transition: all 0.3s ease;
            }
            .action-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
            }
            .notes-section {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 8px;
                padding: 20px;
                margin: 25px 0;
            }
            .notes-title {
                font-size: 16px;
                font-weight: bold;
                color: #856404;
                margin-bottom: 15px;
                display: flex;
                align-items: center;
            }
            .notes-title::before {
                content: '💡';
                margin-right: 8px;
                font-size: 18px;
            }
            .notes-list {
                margin: 0;
                padding-left: 20px;
            }
            .notes-list li {
                margin-bottom: 8px;
                color: #856404;
            }
            .footer {
                background-color: #2c3e50;
                color: #bdc3c7;
                text-align: center;
                padding: 20px;
                font-size: 12px;
            }
            .footer-logo {
                color: #ecf0f1;
                font-weight: bold;
                margin-bottom: 5px;
            }
            @media (max-width: 600px) {
                .email-container { margin: 10px; }
                .content { padding: 20px; }
                .info-grid { grid-template-columns: 1fr; }
                .work-table { font-size: 12px; }
                .work-table th, .work-table td { padding: 8px 6px; }
            }
        </style>
    </head>
    <body>
        <div class='email-container'>
            <div class='header'>
                <h1>人力需求自動核可通知</h1>
                <p>良有營造股份有限公司電子簽核系統</p>
                <div class='approved-badge'>✓ 已自動核可</div>
            </div>

            <div class='content'>
                <div class='greeting'>
                    親愛的 <strong>{$approver_name}</strong> 主管，您好：
                </div>

                <p>以下人力需求申請已通過系統自動核可，特此通知您知悉：</p>

                <div class='info-section'>
                    <div class='info-title'>申請基本資訊</div>
                    <div class='info-grid'>
                        <div class='info-item'>
                            <div class='info-label'>申請人</div>
                            <div class='info-value'>{$requester_name}</div>
                        </div>
                        <div class='info-item'>
                            <div class='info-label'>工地名稱</div>
                            <div class='info-value'>{$site_name}</div>
                        </div>
                        <div class='info-item'>
                            <div class='info-label'>工作日期</div>
                            <div class='info-value'>{$work_date}</div>
                        </div>
                        <div class='info-item'>
                            <div class='info-label'>申請編號</div>
                            <div class='info-value'>#{$request_id}</div>
                        </div>
                        <div class='info-item'>
                            <div class='info-label'>申請狀態</div>
                            <div class='info-value status-approved'>已自動核可</div>
                        </div>
                        <div class='info-item'>
                            <div class='info-label'>核可時間</div>
                            <div class='info-value'>" . date('Y-m-d H:i:s') . "</div>
                        </div>
                    </div>
                </div>

                <div class='work-details'>
                    <div class='work-header'>工作內容及人力需求明細</div>
                    <table class='work-table'>
                        <thead>
                            <tr>
                                <th style='width: 60px;'>項次</th>
                                <th>工作內容描述</th>
                                <th style='width: 100px;'>需求人數</th>
                                <th style='width: 120px;'>指定廠商</th>
                            </tr>
                        </thead>
                        <tbody>
                            {$work_items_html}
                        </tbody>
                    </table>
                    <div class='total-summary'>
                        總計需求人力：<strong>{$total_workers} 人</strong>
                    </div>
                </div>

                <div style='text-align: center;'>
                    <a href='" . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . "://{$_SERVER['HTTP_HOST']}/manpower-requests/view_request.php?id={$request_id}' class='action-button'>
                        📄 查看完整申請詳情
                    </a>
                </div>

                <div class='notes-section'>
                    <div class='notes-title'>重要說明</div>
                    <ul class='notes-list'>
                        <li><strong>此申請已通過系統自動核可</strong>，無需您進行任何審核操作</li>
                        <li>申請人可以立即開始安排相關工作及人力調度</li>
                        <li>如對申請內容有任何疑問，請直接聯繫申請人 <strong>{$requester_name}</strong></li>
                        <li>您可以隨時點擊上方按鈕查看完整的申請詳情</li>
                        <li>此為系統自動發送的通知郵件，請勿直接回覆</li>
                    </ul>
                </div>
            </div>

            <div class='footer'>
                <div class='footer-logo'>良有營造股份有限公司電子簽核系統</div>
                <div>系統時間：" . date('Y-m-d H:i:s') . "</div>
                <div style='margin-top: 5px; font-size: 11px;'>
                    此郵件由系統自動發送 | 如有技術問題請聯繫系統管理員
                </div>
            </div>
        </div>
    </body>
    </html>
    ";

    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: 良有營造電子簽核系統 <<EMAIL>>" . "\r\n";
    $headers .= "Reply-To: <EMAIL>" . "\r\n";

    if (!mail($to, $subject, $message, $headers)) {
        throw new Exception("無法發送email通知到 {$to}");
    }
}

// 處理表單提交
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 驗證輸入
        $work_date = $_POST['work_date'] ?? '';
        $site_id = $_POST['site_id'] ?? '';
        $approver_id = $_POST['approver_id'] ?? '';

        // 驗證日期格式
        if (empty($work_date) || !preg_match('/^\d{4}-\d{2}-\d{2}$/', $work_date)) {
            throw new Exception('請輸入有效的工作日期');
        }

        // 驗證工作日期 - 允許7天前到未來的日期
        $today = date('Y-m-d');
        $seven_days_ago = date('Y-m-d', strtotime('-7 days'));
        if ($work_date < $seven_days_ago) {
            throw new Exception('工作日期不能早於7天前（' . $seven_days_ago . '）');
        }

        // 驗證工地
        if (empty($site_id)) {
            throw new Exception('請選擇工地');
        }

        // 驗證審核人
        if (empty($approver_id)) {
            throw new Exception('請選擇審核主管');
        }

        // 取得工地名稱並驗證用戶是否有權限選擇此工地
        $site_data = $db->fetchOne("SELECT name FROM sites WHERE id = ?", [$site_id]);
        if (!$site_data) {
            throw new Exception('選擇的工地不存在');
        }
        $site_name = $site_data['name'];

        // 驗證用戶是否有權限選擇此工地（總公司不在此限）
        if ($user_site !== '總公司' && $site_name !== $user_site) {
            throw new Exception('您只能為自己的工地提交人力需求');
        }

        // 驗證審核人是否為該工地的主管或總公司的人員
        $approver_data = $db->fetchOne(
            "SELECT id, name, email, site FROM users WHERE id = ? AND ((site = ? AND role IN ('site_supervisor', 'admin')) OR site = '總公司')",
            [$approver_id, $site_name]
        );
        if (!$approver_data) {
            throw new Exception('選擇的審核主管無效');
        }

        // 驗證至少有一個工作項目
        if (empty($_POST['work_description']) || !is_array($_POST['work_description']) || count(array_filter($_POST['work_description'])) === 0) {
            throw new Exception('請至少添加一個工作項目');
        }

        // 開始事務
        $db->beginTransaction();

        // 插入主表數據（自動核可）
        $request_data = [
            'site' => $site_name,
            'requester_id' => $user_id,
            'approver_id' => $approver_id,
            'request_date' => date('Y-m-d'),
            'work_date' => $work_date,
            'status' => 'approved',
            'approved_at' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $request_id = $db->insert('manpower_requests', $request_data);

        if (!$request_id) {
            throw new Exception('創建人力需求失敗');
        }

        // 插入工作項目
        $descriptions = $_POST['work_description'] ?? [];
        $workers = $_POST['workers_required'] ?? [];
        $companies = $_POST['company_preference'] ?? [];

        $itemCount = count($descriptions);

        for ($i = 0; $i < $itemCount; $i++) {
            $description = $descriptions[$i] ?? '';
            $worker_count = intval($workers[$i] ?? 0);
            $company = $companies[$i] ?? '';

            if (!empty($description) && $worker_count > 0) {
                $item_data = [
                    'request_id' => $request_id,
                    'work_description' => $description,
                    'workers_required' => $worker_count,
                    'company_preference' => $company,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                $item_id = $db->insert('manpower_request_items', $item_data);

                if (!$item_id) {
                    throw new Exception('添加工作項目失敗');
                }
            }
        }

        // 提交事務
        $db->commit();

        // 發送email通知審核主管（告知已自動核可）
        try {
            sendAutoApprovalNotificationEmail($approver_data, $fullname, $site_name, $work_date, $request_id);
        } catch (Exception $e) {
            // email發送失敗不影響主要流程，只記錄錯誤
            error_log("發送email通知失敗: " . $e->getMessage());
        }

        // 發送系統內部通知給審核主管
        try {
            $notification_message = "來自 {$fullname} 的人力需求申請已自動核可，工地：{$site_name}，工作日期：{$work_date}，請知悉。";
            $db->execute(
                "INSERT INTO notifications (user_id, message, created_at) VALUES (?, ?, NOW())",
                [$approver_id, $notification_message]
            );
        } catch (Exception $e) {
            // 系統通知發送失敗不影響主要流程，只記錄錯誤
            error_log("發送系統通知失敗: " . $e->getMessage());
        }

        $success_message = '人力需求已成功提交並自動核可。已發送通知給審核主管。';

        // 重定向到查看頁面
        header("Location: view_request.php?id=$request_id&success=1");
        exit;

    } catch (Exception $e) {
        // 回滾事務
        $db->rollBack();
        $error_message = $e->getMessage();
    }
}

// 獲取工地列表（總公司可以選擇所有工地，其他用戶只能選擇自己的工地）
$user_site = $_SESSION['site'] ?? '';
if (empty($user_site)) {
    // 如果session中沒有工地信息，從數據庫獲取
    $user_data = $db->fetchOne("SELECT site FROM users WHERE id = ?", [$user_id]);
    $user_site = $user_data['site'] ?? '';
}

if ($user_site === '總公司') {
    // 總公司用戶可以選擇所有工地
    $sites_query = "SELECT id, name FROM sites ORDER BY name";
    $sites_result = $db->query($sites_query);
} else {
    // 其他用戶只能選擇自己的工地
    $sites_query = "SELECT id, name FROM sites WHERE name = ? ORDER BY name";
    $sites_result = $db->query($sites_query, [$user_site]);
}

$sites = [];
while ($site = $sites_result->fetch(PDO::FETCH_ASSOC)) {
    $sites[] = $site;
}

// 獲取主管列表（用於選擇審核人）
if ($user_site === '總公司') {
    // 總公司用戶：獲取所有工地的主管，但需要通過JavaScript動態更新
    $supervisors = []; // 初始為空，通過JavaScript動態載入
} else {
    // 其他用戶：獲取自己工地的主管 + 總公司的所有人員
    $supervisors_query = "
        SELECT u.id, u.name, u.email, u.site,
               CASE
                   WHEN u.site = ? THEN '工地主管'
                   WHEN u.site = '總公司' THEN '總公司'
                   ELSE '其他'
               END as group_type
        FROM users u
        WHERE (u.site = ? AND u.role IN ('site_supervisor', 'admin'))
           OR u.site = '總公司'
        ORDER BY
            CASE
                WHEN u.site = ? THEN 1
                WHEN u.site = '總公司' THEN 2
                ELSE 3
            END,
            u.name
    ";
    $supervisors = $db->fetchAll($supervisors_query, [$user_site, $user_site, $user_site]);
}

?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新增人力需求 - 良友營造電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../dark-theme.css">
    <style>
        :root {
            --primary-color: #4361EE;
            --success-color: #4CAF50;
            --warning-color: #FFC107;
            --danger-color: #F44336;
            --info-color: #2196F3;

            --text-primary: #E0E0E0;
            --text-secondary: #AAAAAA;
            --text-muted: #888888;

            --bg-primary: #121212;
            --bg-secondary: #1E1E1E;
            --bg-tertiary: #252525;
            --bg-input: #333333;

            --border-color: #444444;
            --card-bg: #1E1E1E;
            --card-header: #252525;
            --table-header: #2c2c2c;
            --hover-bg: rgba(255, 255, 255, 0.05);

            --shadow-color: rgba(0, 0, 0, 0.4);
            --navbar-bg: #1e1e1e;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        body {
            font-family: 'Microsoft JhengHei', 'PingFang TC', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .navbar {
            background-color: var(--navbar-bg);
            padding: 10px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px var(--shadow-color);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .navbar-brand img {
            height: 40px;
            margin-right: 15px;
        }

        .navbar-brand h1 {
            font-size: 18px;
            margin: 0;
            color: var(--navbar-text);
        }

        .navbar-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .nav-item {
            position: relative;
            margin: 0 5px;
        }

        .nav-link {
            color: var(--navbar-text);
            text-decoration: none;
            padding: 10px 15px;
            display: flex;
            align-items: center;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .nav-link i {
            margin-right: 8px;
            font-size: 16px;
        }

        .nav-link:hover {
            background-color: var(--navbar-hover);
            color: var(--navbar-text);
        }

        .nav-link.active {
            background-color: var(--primary-color);
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .back-nav {
            display: inline-flex;
            align-items: center;
            color: var(--text-secondary);
            margin-bottom: 20px;
            text-decoration: none;
        }

        .back-nav:hover {
            color: var(--primary-color);
        }

        .back-nav i {
            margin-right: 5px;
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 24px;
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
        }

        .page-title i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .form-card {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 4px 15px var(--shadow-color);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--card-header);
            padding: 15px 20px;
            display: flex;
            align-items: center;
        }

        .card-header h2 {
            font-size: 18px;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .card-header h2 i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input[type="text"],
        .form-group input[type="number"],
        .form-group input[type="date"],
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px 12px;
            background-color: var(--bg-input);
            border: 1px solid var(--border-color);
            border-radius: 5px;
            color: var(--text-primary);
            font-family: inherit;
            font-size: 14px;
            box-sizing: border-box;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
        }

        .btn i {
            margin-right: 8px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-success {
            background-color: var(--success-color);
            color: white;
        }

        .btn-danger {
            background-color: var(--danger-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #3651d4;
        }

        .btn-success:hover {
            background-color: #3d8b40;
        }

        .btn-danger:hover {
            background-color: #d32f2f;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            display: flex;
            align-items: flex-start;
        }

        .alert i {
            margin-right: 10px;
            font-size: 20px;
            margin-top: 2px;
        }

        .alert-danger {
            background-color: rgba(244, 67, 54, 0.1);
            border-left: 4px solid var(--danger-color);
            color: #e57373;
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.1);
            border-left: 4px solid var(--success-color);
            color: #81c784;
        }

        .work-item {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            position: relative;
        }

        .work-item-header {
            font-weight: 500;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .remove-item {
            background-color: transparent;
            border: none;
            color: var(--danger-color);
            cursor: pointer;
            font-size: 16px;
            padding: 5px;
        }

        .remove-item:hover {
            color: #f77;
        }

        .form-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
        }

        .required {
            color: var(--danger-color);
            margin-left: 3px;
        }

        .empty-state {
            text-align: center;
            padding: 30px;
            color: var(--text-muted);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .empty-state p {
            margin: 5px 0;
        }

        #work-items-container {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="navbar-brand">
            <img src="https://paper.lybuild.com.tw/images/logo.png" alt="良友營造標誌">
            <h1>良友營造電子簽核系統</h1>
        </div>
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-tachometer-alt"></i> 簽核總覽
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="index.php">
                    <i class="fas fa-users-cog"></i> 人力需求派工
                </a>
            </li>
        </ul>
    </nav>

    <div class="main-content">
        <a href="index.php" class="back-nav">
            <i class="fas fa-arrow-left"></i> 返回人力需求派工列表
        </a>

        <div class="page-header">
            <h1 class="page-title"><i class="fas fa-plus-circle"></i> 新增人力需求</h1>
            <p>請填寫以下表單，提交工地人力需求申請。人力需求將送交工地主管審核。</p>

        </div>

        <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <div>
                <div style="font-weight: 500; margin-bottom: 5px;">提交失敗</div>
                <div><?php echo htmlspecialchars($error_message); ?></div>
            </div>
        </div>
        <?php endif; ?>

        <?php if (!empty($success_message)): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <div>
                <div style="font-weight: 500; margin-bottom: 5px;">提交成功</div>
                <div><?php echo htmlspecialchars($success_message); ?></div>
            </div>
        </div>
        <?php endif; ?>

        <form method="POST" action="add_request.php">
            <div class="form-card">
                <div class="card-header">
                    <h2><i class="fas fa-info-circle"></i> 基本資訊</h2>
                </div>
                <div class="card-body">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="work_date">工作日期 <span class="required">*</span></label>
                            <input type="date" id="work_date" name="work_date" required min="<?php echo date('Y-m-d', strtotime('-7 days')); ?>">
                            <small style="color: var(--text-muted);">可選擇7天前至未來的日期</small>
                        </div>
                        <div class="form-group">
                            <label for="site_id">工地 <span class="required">*</span></label>
                            <select id="site_id" name="site_id" class="form-control" required>
                                <option value="">請選擇工地</option>
                                <?php foreach ($sites as $site): ?>
                                <option value="<?php echo $site['id']; ?>" <?php echo ($site['name'] === $user_site) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($site['name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <small style="color: var(--text-muted);">
                                <?php if ($user_site === '總公司'): ?>
                                    總公司可以為任何工地提交需求
                                <?php else: ?>
                                    您只能為自己的工地提交需求
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="approver_id">審核主管 <span class="required">*</span></label>
                            <select id="approver_id" name="approver_id" class="form-control" required>
                                <option value="">請選擇審核主管</option>
                                <?php
                                $current_group = '';
                                foreach ($supervisors as $supervisor):
                                    if (isset($supervisor['group_type']) && $supervisor['group_type'] !== $current_group):
                                        if ($current_group !== '') echo '</optgroup>';
                                        $current_group = $supervisor['group_type'];
                                        echo '<optgroup label="' . htmlspecialchars($current_group) . '">';
                                    endif;
                                ?>
                                <option value="<?php echo $supervisor['id']; ?>">
                                    <?php echo htmlspecialchars($supervisor['name']); ?>
                                    <?php if (!empty($supervisor['email'])): ?>
                                        (<?php echo htmlspecialchars($supervisor['email']); ?>)
                                    <?php endif; ?>
                                </option>
                                <?php endforeach; ?>
                                <?php if ($current_group !== '') echo '</optgroup>'; ?>
                            </select>
                            <small style="color: var(--text-muted);">可選擇工地主管或總公司人員進行審核</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-card">
                <div class="card-header">
                    <h2><i class="fas fa-tasks"></i> 工作項目</h2>
                </div>
                <div class="card-body">
                    <p>請添加一個或多個工作項目，指定所需人力數量和工作內容。</p>

                    <div id="work-items-container">
                        <div class="empty-state" id="no-items-message" style="display: none;">
                            <i class="fas fa-clipboard-list"></i>
                            <p>尚未添加工作項目</p>
                            <p>請點擊下方按鈕添加工作項目</p>
                        </div>
                    </div>

                    <button type="button" class="btn btn-primary" id="add-work-item">
                        <i class="fas fa-plus"></i> 添加工作項目
                    </button>
                </div>
            </div>

            <div class="form-footer">
                <button type="button" class="btn btn-danger" onclick="location.href='index.php'">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-paper-plane"></i> 提交申請
                </button>
            </div>
        </form>
    </div>

    <!-- 工作項目模板 -->
    <template id="work-item-template">
        <div class="work-item">
            <div class="work-item-header">
                <span>工作項目 #<span class="item-number"></span></span>
                <button type="button" class="remove-item" onclick="removeWorkItem(this)">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
            <div class="form-group">
                <label>工作描述 <span class="required">*</span></label>
                <textarea name="work_description[]" placeholder="請描述此項工作內容" required></textarea>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>需求人數 <span class="required">*</span></label>
                    <input type="number" name="workers_required[]" min="1" placeholder="請輸入數字" required>
                </div>
                <div class="form-group">
                    <label>偏好公司</label>
                    <input type="text" name="company_preference[]" placeholder="可選填，偏好指定的人力公司">
                </div>
            </div>
        </div>
    </template>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 添加第一個工作項目
            addWorkItem();

            // 綁定添加按鈕事件
            document.getElementById('add-work-item').addEventListener('click', function() {
                addWorkItem();
            });

            // 如果是總公司用戶，綁定工地選擇事件來動態載入主管
            <?php if ($user_site === '總公司'): ?>
            const siteSelect = document.getElementById('site_id');
            const approverSelect = document.getElementById('approver_id');

            siteSelect.addEventListener('change', function() {
                const siteId = this.value;

                // 清空審核人選項
                approverSelect.innerHTML = '<option value="">請選擇審核主管</option>';

                if (siteId) {
                    // 載入該工地的主管
                    loadSupervisors(siteId);
                }
            });
            <?php endif; ?>
        });

        let itemCounter = 0;

        <?php if ($user_site === '總公司'): ?>
        // 載入指定工地的主管列表
        function loadSupervisors(siteId) {
            const approverSelect = document.getElementById('approver_id');

            // 顯示載入中
            approverSelect.innerHTML = '<option value="">載入中...</option>';

            // 發送AJAX請求
            fetch('get_supervisors.php?site_id=' + siteId)
                .then(response => response.json())
                .then(data => {
                    approverSelect.innerHTML = '<option value="">請選擇審核主管</option>';

                    if (data.success && data.supervisors) {
                        let currentGroup = '';
                        let currentOptgroup = null;

                        data.supervisors.forEach(supervisor => {
                            // 檢查是否需要創建新的optgroup
                            if (supervisor.group_type && supervisor.group_type !== currentGroup) {
                                currentGroup = supervisor.group_type;
                                currentOptgroup = document.createElement('optgroup');
                                currentOptgroup.label = currentGroup;
                                approverSelect.appendChild(currentOptgroup);
                            }

                            const option = document.createElement('option');
                            option.value = supervisor.id;
                            option.textContent = supervisor.name + (supervisor.email ? ' (' + supervisor.email + ')' : '');

                            if (currentOptgroup) {
                                currentOptgroup.appendChild(option);
                            } else {
                                approverSelect.appendChild(option);
                            }
                        });
                    } else {
                        approverSelect.innerHTML = '<option value="">該工地沒有可用的審核主管</option>';
                    }
                })
                .catch(error => {
                    console.error('載入主管列表失敗:', error);
                    approverSelect.innerHTML = '<option value="">載入失敗，請重試</option>';
                });
        }
        <?php endif; ?>

        function addWorkItem() {
            // 增加計數器
            itemCounter++;

            // 獲取模板
            const template = document.getElementById('work-item-template');
            const container = document.getElementById('work-items-container');

            // 克隆模板
            const workItem = document.importNode(template.content, true);

            // 設置項目編號
            workItem.querySelector('.item-number').textContent = itemCounter;

            // 添加到容器
            container.appendChild(workItem);

            // 隱藏空狀態消息
            document.getElementById('no-items-message').style.display = 'none';

            // 更新表單狀態
            updateFormState();
        }

        function removeWorkItem(button) {
            // 獲取工作項目元素
            const workItem = button.closest('.work-item');

            // 從DOM中移除
            workItem.remove();

            // 更新剩餘的項目編號
            const items = document.querySelectorAll('.work-item');
            items.forEach((item, index) => {
                item.querySelector('.item-number').textContent = index + 1;
            });

            // 如果沒有項目了，顯示空狀態消息
            if (items.length === 0) {
                document.getElementById('no-items-message').style.display = 'block';
            }

            // 更新表單狀態
            updateFormState();
        }

        function updateFormState() {
            // 獲取所有項目
            const items = document.querySelectorAll('.work-item');

            // 提交按鈕
            const submitButton = document.querySelector('button[type="submit"]');

            // 如果沒有項目，禁用提交按鈕
            submitButton.disabled = items.length === 0;
        }
    </script>
</body>
</html>