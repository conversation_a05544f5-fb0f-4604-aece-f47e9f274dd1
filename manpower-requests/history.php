<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// 初始化數據庫連接
require_once('../db_service.php');
$db = DBService::getInstance();
$pdo = $db->getPdo();

// 獲取用戶資料
function getUserData($userId, $pdo) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching user data: " . $e->getMessage());
        return false;
    }
}

// 獲取當前用戶資料
$user = getUserData($_SESSION['user_id'], $pdo);
if (!$user) {
    $_SESSION['error'] = "無法獲取用戶資料";
    header('Location: index.php');
    exit;
}

// 獲取工地列表供篩選使用（總公司可以看到所有工地，其他用戶只能看到自己的工地）
try {
    if ($user['site'] === '總公司') {
        // 總公司用戶可以看到所有工地
        $stmt = $pdo->prepare("SELECT id, name FROM sites ORDER BY name");
        $stmt->execute();
        $sites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // 其他用戶只能看到自己的工地
        $stmt = $pdo->prepare("SELECT id, name FROM sites WHERE name = ? ORDER BY name");
        $stmt->execute([$user['site']]);
        $sites = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    error_log("Error fetching sites: " . $e->getMessage());
    $sites = [];
}

// 設定分頁
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
// 允許用戶選擇每頁顯示的記錄數
$allowedPerPage = [10, 20, 30, 50, 100];
$perPage = isset($_GET['per_page']) && in_array(intval($_GET['per_page']), $allowedPerPage) ? intval($_GET['per_page']) : 10;
$offset = ($page - 1) * $perPage;

// 獲取搜尋與篩選參數
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$startDate = isset($_GET['start_date']) && !empty($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-90 days'));
$endDate = isset($_GET['end_date']) && !empty($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$siteId = isset($_GET['site_id']) && !empty($_GET['site_id']) ? intval($_GET['site_id']) : null;
$status = isset($_GET['status']) && in_array($_GET['status'], ['all', 'pending', 'approved', 'rejected']) ? $_GET['status'] : 'all';

// 建立查詢基礎
$params = [$startDate, $endDate];
$whereConditions = ["r.work_date BETWEEN ? AND ?"];

// 增加搜尋條件
if (!empty($search)) {
    $whereConditions[] = "(r.id LIKE ? OR s.name LIKE ? OR ua.name LIKE ?)";
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);

    // 記錄搜尋條件
    error_log("搜尋條件: " . $search);
    error_log("SQL WHERE 條件: " . implode(' AND ', $whereConditions));
}

// 增加工地篩選條件
if ($siteId) {
    // 獲取工地名稱
    try {
        $siteStmt = $pdo->prepare("SELECT name FROM sites WHERE id = ?");
        $siteStmt->execute([$siteId]);
        $siteName = $siteStmt->fetchColumn();

        if ($siteName) {
            $whereConditions[] = "r.site = ?";
            $params[] = $siteName;
        }
    } catch (PDOException $e) {
        error_log("Error fetching site name: " . $e->getMessage());
    }
}

// 增加狀態篩選條件
if ($status !== 'all') {
    $whereConditions[] = "r.status = ?";
    $params[] = $status;
}

// 根據用戶角色添加權限控制
$role = $_SESSION['role'];
$user_id = $_SESSION['user_id'];
$user_site = $user['site'];

// 權限控制：總公司用戶可以查看所有工地，其他用戶只能查看自己工地
if ($user_site !== '總公司') {
    // 非總公司用戶只能看到自己工地的人力需求
    $whereConditions[] = "s.name = ?";
    $params[] = $user_site;
}
// 總公司用戶可以看到所有工地的人力需求，不需要添加額外條件

// 組合 WHERE 子句
$whereClause = implode(' AND ', $whereConditions);

// 計算總記錄數
$countSql = "SELECT COUNT(DISTINCT r.id) FROM manpower_requests r
             JOIN sites s ON r.site = s.name
             JOIN users ua ON r.requester_id = ua.id
             LEFT JOIN users ur ON r.approver_id = ur.id
             WHERE $whereClause";

try {
    $stmt = $pdo->prepare($countSql);
    $stmt->execute($params);
    $totalItems = $stmt->fetchColumn();
    $totalPages = ceil($totalItems / $perPage);
} catch (PDOException $e) {
    error_log("Error counting records: " . $e->getMessage());
    $totalItems = 0;
    $totalPages = 0;
}

// 取得分頁後的需求列表
$sql = "SELECT r.id, r.work_date, r.created_at, r.status, r.approval_date as reviewed_at, r.rejection_reason,
        s.name as site_name, ua.name as applicant_name, ur.name as reviewer_name,
        (SELECT COUNT(*) FROM manpower_request_items WHERE request_id = r.id) as item_count,
        (SELECT SUM(workers_required) FROM manpower_request_items WHERE request_id = r.id) as total_workers_required,
        (SELECT COUNT(*) FROM manpower_records WHERE request_id = r.id) as recorded_items,
        (SELECT SUM(worker_count) FROM manpower_records WHERE request_id = r.id) as total_actual_workers
        FROM manpower_requests r
        JOIN sites s ON r.site = s.name
        JOIN users ua ON r.requester_id = ua.id
        LEFT JOIN users ur ON r.approver_id = ur.id
        WHERE $whereClause
        GROUP BY r.id
        ORDER BY r.work_date DESC, r.created_at DESC
        LIMIT ?, ?";

// 記錄完整SQL查詢
error_log("完整SQL查詢: " . $sql);
error_log("SQL參數: " . implode(', ', $params));

// 添加分頁參數
$params[] = $offset;
$params[] = $perPage;

try {
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $requests = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching requests: " . $e->getMessage());
    $requests = [];
}

function translateStatus($status) {
    switch ($status) {
        case 'pending':
            return '待審核';
        case 'approved':
            return '已核准';
        case 'rejected':
            return '已拒絕';
        default:
            return $status;
    }
}

// 映射表 - 移至配置文件更佳
$role_mapping = [
    'admin' => '系統管理者',
    'site_supervisor' => '工地主管',
    'hq_supervisor' => '總公司主管',
    'employee' => '員工'
];

// 檢查是否有訊息
$message = '';
if (isset($_SESSION['success'])) {
    $message = '<div class="alert alert-success">' . $_SESSION['success'] . '</div>';
    unset($_SESSION['success']);
} elseif (isset($_SESSION['error'])) {
    $message = '<div class="alert alert-danger">' . $_SESSION['error'] . '</div>';
    unset($_SESSION['error']);
}
?>

<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人力需求派工 - 歷史記錄 - 良有營造股份有限公司電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../dark-theme.css">
    <link rel="stylesheet" href="../light-theme.css">
    <link rel="stylesheet" href="../responsive.css">
    <link rel="stylesheet" href="../navbar.css">
    <link rel="stylesheet" href="user-info-style.css">
    <link rel="stylesheet" href="user-info-color.css">
    <link rel="stylesheet" href="force-animation.css">
    <link rel="stylesheet" href="compact-user-info.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
    <!-- 使用簡化版主題切換器 -->
    <script src="theme-switcher-custom.js" defer></script>
    <!-- 強制設置用戶信息顏色 -->
    <script src="force-user-info-color.js" defer></script>
    <!-- 強制設置用戶信息字體大小 -->
    <style>
        /* 使用最高優先級的選擇器強制設置字體大小 */
        .user-info i.fas.fa-user-circle,
        .user-info i.fas.fa-user-circle[style],
        .user-info > i.fas.fa-user-circle,
        .user-info > i.fas.fa-user-circle[style] {
            font-size: 14px !important;
        }

        .user-name,
        .user-name[style],
        .user-info .user-name,
        .user-info .user-name[style],
        .user-info > .user-info-details > .user-name,
        .user-info > .user-info-details > .user-name[style] {
            font-size: 14px !important;
        }

        .user-position,
        .user-position[style],
        .user-info .user-position,
        .user-info .user-position[style],
        .user-info > .user-info-details > .user-position,
        .user-info > .user-info-details > .user-position[style] {
            font-size: 11px !important;
        }

        .user-position i,
        .user-position i[style],
        .user-info .user-position i,
        .user-info .user-position i[style],
        .user-info > .user-info-details > .user-position i,
        .user-info > .user-info-details > .user-position i[style] {
            font-size: 11px !important;
        }

        .user-site,
        .user-site[style],
        .user-info .user-site,
        .user-info .user-site[style],
        .user-info > .user-info-details > .user-site,
        .user-info > .user-info-details > .user-site[style] {
            font-size: 14px !important;
        }

        .notification-icon i.fas.fa-bell,
        .notification-icon i.fas.fa-bell[style] {
            font-size: 14px !important;
        }

        /* 美化分頁樣式 */
        .pagination {
            margin-bottom: 0;
        }

        .pagination .page-item .page-link {
            border-radius: 4px;
            margin: 0 2px;
            color: var(--primary-color);
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            transition: all 0.2s ease;
            font-weight: 500;
            min-width: 36px;
            text-align: center;
        }

        .pagination .page-item.active .page-link {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .pagination .page-item .page-link:hover {
            background-color: var(--hover-bg);
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .pagination .page-item.disabled .page-link {
            color: var(--text-muted);
            background-color: var(--bg-secondary);
            border-color: var(--border-color);
            opacity: 0.6;
        }

        /* 每頁顯示數量選擇器樣式 */
        .per-page-selector .btn-group .btn {
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .per-page-selector .btn-group .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .per-page-selector .btn-group .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .per-page-selector .btn-group .btn-outline-secondary {
            color: var(--text-primary);
            border-color: var(--border-color);
        }

        .per-page-selector .btn-group .btn-outline-secondary:hover {
            background-color: var(--hover-bg);
            color: var(--primary-color);
        }

        /* 分頁信息樣式 */
        .pagination-info {
            color: var(--text-secondary);
            font-size: 0.9rem;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
        }

        /* 響應式調整 */
        @media (max-width: 768px) {
            .pagination-container {
                flex-direction: column;
                align-items: center;
            }

            .pagination-container > div {
                flex: none !important;
                width: 100%;
                text-align: center !important;
                margin: 5px 0;
            }

            .per-page-selector {
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .pagination-info {
                width: 100%;
                max-width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
    <script>
    // 確保在DOM載入後立即執行
    document.addEventListener('DOMContentLoaded', function() {
        // 立即強制設置導覽條顏色
        function forceNavbarColor() {
            const navbar = document.querySelector('.navbar');
            if (!navbar) return;

            // 檢查當前主題
            const currentTheme = localStorage.getItem('theme') || 'dark';

            // 強制設置內聯樣式（優先級最高）
            if (currentTheme === 'light') {
                navbar.style.setProperty('background-color', '#4361EE', 'important');
                console.log('設置淺色模式導覽條顏色: #4361EE');
            } else {
                navbar.style.setProperty('background-color', '#1e1e1e', 'important');
                console.log('設置深色模式導覽條顏色: #1e1e1e');
            }
        }

        // 初始設置顏色
        forceNavbarColor();

        // 監聽主題變化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class' && mutation.target === document.body) {
                    forceNavbarColor();
                }
            });
        });

        // 監視body的class變化
        observer.observe(document.body, { attributes: true });
    });
    </script>
    <style>
        :root {
            --primary-color: #1a73e8;
            --primary-hover: #1557b0;
            --secondary-color: #34a853;
            --secondary-hover: #2d9249;
            --danger-color: #ea4335;
            --danger-hover: #c5221f;
            --warning-color: #fbbc05;
            --warning-hover: #f29900;
            --info-color: #2196F3;

            --text-primary: #E0E0E0;
            --text-secondary: #AAAAAA;
            --text-muted: #888888;
            --text-color: #ffffff;

            --dark-bg: #121212;
            --bg-primary: #121212;
            --bg-secondary: #1E1E1E;
            --bg-tertiary: #252525;
            --bg-input: #333333;

            --border-color: #444444;
            --card-bg: #1E1E1E;
            --card-header: #252525;
            --table-header: #2c2c2c;
            --table-row-hover: #2a2a2a;
            --hover-bg: rgba(255, 255, 255, 0.05);

            --shadow-color: rgba(0, 0, 0, 0.4);
            --navbar-bg: #1e1e1e;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);

            --success-badge: rgba(52, 168, 83, 0.15);
            --warning-badge: rgba(251, 188, 5, 0.15);
            --danger-badge: rgba(234, 67, 53, 0.15);
            --info-badge: rgba(26, 115, 232, 0.15);
        }

        /* 淺色模式樣式 */
        body.theme-light {
            --primary-color: #1a73e8;
            --success-color: #34a853;
            --warning-color: #fbbc05;
            --danger-color: #ea4335;
            --info-color: #4285f4;

            --text-primary: #333333;
            --text-secondary: #666666;
            --text-muted: #888888;

            --bg-primary: #f5f5f5;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f0f0f0;
            --bg-input: #ffffff;

            --border-color: #dddddd;
            --card-bg: #ffffff;
            --card-header: #f0f0f0;
            --table-header: #f0f0f0;
            --hover-bg: rgba(0, 0, 0, 0.05);

            --shadow-color: rgba(0, 0, 0, 0.1);
            --navbar-bg: #4361EE;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        /* 為淺色模式添加特定樣式，確保導覽列中的選項顯示白色 */
        body.theme-light .navbar .nav-link,
        body.theme-light .navbar .nav-link i {
            color: var(--navbar-text) !important;
        }

        body.theme-light .navbar .nav-item:hover .nav-link,
        body.theme-light .navbar .nav-item:hover .nav-link i {
            color: var(--navbar-text) !important;
        }

        /* 為淺色模式下的下拉式選單添加特定樣式 */
        body.theme-light .dropdown-content {
            background-color: #ffffff;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            border-radius: 0 0 8px 8px;
        }

        body.theme-light .dropdown-content a {
            color: #333333 !important;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        body.theme-light .dropdown-content a:last-child {
            border-bottom: none;
        }

        body.theme-light .dropdown-content a i {
            color: #4361EE !important;
            margin-right: 8px;
            width: 20px;
            text-align: center;
        }

        body.theme-light .dropdown-content a:hover {
            background-color: #f0f0f0;
            padding-left: 20px;
        }

        body.theme-light .dropdown-content a:hover i {
            color: #1a73e8 !important;
        }

        /* 帳號申請管理待處理標誌在淺色模式下的樣式 */
        body.theme-light .pending-applications-badge {
            background-color: #ea4335;
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 帳號申請管理選項的顏色加強 */
        body.theme-light .dropdown-content a:nth-last-child(1) {
            font-weight: 500;
        }

        /* 為登出按鈕添加紅色樣式（淺色模式） */
        body.theme-light .navbar .nav-link.logout,
        body.theme-light .navbar .nav-link.logout i {
            color: #ea4335 !important; /* 使用危險色作為登出按鈕顏色 */
        }

        body.theme-light .navbar .nav-link.logout:hover,
        body.theme-light .navbar .nav-link.logout:hover i {
            color: #c5221f !important; /* 使用危險色的懸停顏色 */
        }

        /* 為登出按鈕添加紅色樣式（深色模式） */
        body.theme-dark .navbar .nav-link.logout,
        body.theme-dark .navbar .nav-link.logout i,
        .navbar .nav-link.logout,
        .navbar .nav-link.logout i {
            color: #ea4335 !important; /* 使用危險色作為登出按鈕顏色 */
        }

        body.theme-dark .navbar .nav-link.logout:hover,
        body.theme-dark .navbar .nav-link.logout:hover i,
        .navbar .nav-link.logout:hover,
        .navbar .nav-link.logout:hover i {
            color: #c5221f !important; /* 使用危險色的懸停顏色 */
        }

        body {
            font-family: 'Microsoft JhengHei', 'PingFang TC', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .navbar {
            background-color: var(--navbar-bg);
            padding: 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            padding: 5px 15px;
            height: 45px;
        }

        .navbar-brand img {
            height: 35px;
            margin-right: 10px;
            border-radius: 50%;
        }

        .navbar-brand h1 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: var(--navbar-text);
        }

        .navbar-nav {
            display: flex;
            list-style: none;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: flex;
            align-items: center;
            color: var(--navbar-text);
            text-decoration: none;
            padding: 12px 15px;
            transition: all 0.3s;
            height: 45px;
            box-sizing: border-box;
            font-size: 16px;
        }

        /* 主內容區域樣式 */
        .main-content {
            padding: 70px 20px 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* 頁面標題樣式 */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .page-title i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        /* 儀表板卡片樣式 */
        .dashboard-card {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 2px 10px var(--shadow-color);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--card-header);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            font-size: 18px;
            margin: 0;
            display: flex;
            align-items: center;
        }

        .card-header h2 i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .card-body {
            padding: 20px;
        }

        /* 表格樣式 */
        .document-table {
            width: 100%;
            border-collapse: collapse;
        }

        .document-table th {
            background-color: var(--table-header);
            color: var(--text-primary);
            text-align: left;
            padding: 12px 15px;
            font-weight: 600;
        }

        .document-table td {
            padding: 12px 15px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .document-table tbody tr:hover {
            background-color: var(--hover-bg);
        }

        /* 空狀態樣式 */
        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
            text-align: center;
        }

        .empty-state i {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 15px;
        }

        .empty-state p {
            color: var(--text-secondary);
            font-size: 16px;
            margin: 5px 0;
        }

        /* 徽章樣式 */
        .badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .badge-pending {
            background-color: var(--warning-color);
            color: #000;
        }

        .badge-approved {
            background-color: var(--secondary-color);
            color: #fff;
        }

        .badge-rejected {
            background-color: var(--danger-color);
            color: #fff;
        }

        .badge-record {
            background-color: var(--info-color);
            color: #fff;
        }

        /* 操作按鈕樣式 */
        .action-buttons {
            display: flex;
            gap: 5px;
        }

        .btn {
            border-radius: 4px;
            font-weight: 500;
            padding: 8px 16px;
            transition: all 0.2s;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 14px;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
            color: #fff;
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
            color: #212529;
        }

        .btn-info {
            background-color: var(--info-color);
            border-color: var(--info-color);
        }

        /* 表單控制項樣式 */
        .form-control {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
        }

        .form-control:focus {
            background-color: var(--card-bg);
            color: var(--text-color);
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        /* 徽章樣式 */
        .badge {
            padding: 5px 10px;
            font-weight: 500;
            border-radius: 50px;
        }

        /* 分頁樣式 */
        .pagination {
            justify-content: center;
            margin-top: 20px;
        }

        .pagination .page-item .page-link {
            background-color: var(--card-bg);
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }

        .pagination .page-item.active .page-link {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .pagination .page-item .page-link:hover {
            background-color: var(--hover-bg);
        }

        /* 輸入格樣式 */
        input, select, textarea {
            background-color: var(--card-bg) !important;
            color: var(--text-color) !important;
            border: 1px solid var(--border-color) !important;
        }

        /* 狀態指示器 */
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-indicator.pending {
            background-color: var(--warning-color);
        }

        .status-indicator.approved {
            background-color: var(--success-color);
        }

        .status-indicator.rejected {
            background-color: var(--danger-color);
        }

        /* 日期選擇器樣式 */
        .datepicker-dropdown {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            position: fixed !important;
            top: 200px !important;
            z-index: 9999 !important;
            max-height: 300px !important;
            overflow-y: auto !important;
        }

        .datepicker table tr td, .datepicker table tr th {
            color: var(--text-color);
        }

        .datepicker table tr td.active {
            background-color: var(--accent-color) !important;
        }

        /* 確保日期選擇器顯示在頂部 */
        .datepicker-dropdown.datepicker-orient-bottom:before,
        .datepicker-dropdown.datepicker-orient-bottom:after {
            top: -7px !important;
            bottom: auto !important;
        }

        .datepicker-dropdown.datepicker-orient-bottom:after {
            top: -6px !important;
        }
    </style>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 響應式導覽列切換
        const navbarToggler = document.getElementById('navbarToggler');
        const navbarNav = document.getElementById('navbarNav');

        if (navbarToggler && navbarNav) {
            navbarToggler.addEventListener('click', function() {
                navbarNav.classList.toggle('active');
            });
        }
    });
    </script>
</head>
<body>
    <!-- 訊息顯示區域 -->
    <?php if (isset($_SESSION['message']) && isset($_SESSION['message_type'])): ?>
        <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show m-3" role="alert">
            <?php
                echo $_SESSION['message'];
                unset($_SESSION['message']);
                unset($_SESSION['message_type']);
            ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php elseif (!empty($message)): ?>
        <div class="alert alert-info alert-dismissible fade show m-3" role="alert">
            <?php echo $message; ?>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <!-- 導航欄 -->
    <nav class="navbar" data-theme-element="navbar" style="display: flex !important; justify-content: space-between !important; align-items: center !important;">
        <div class="navbar-brand">
            <img src="https://paper.lybuild.com.tw/images/logo.png" alt="良有營造標誌">
            <h1>良有營造電子簽核系統</h1>
            <button class="navbar-toggler" id="navbarToggler">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <ul class="navbar-nav" id="navbarNav" style="display: flex !important; flex-direction: row !important; justify-content: center !important; align-items: center !important; margin: 0; padding: 0; list-style: none; flex: 1;">
            <li class="nav-item">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-tachometer-alt"></i> 簽核總覽
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-users-cog"></i> 人力需求派工
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="history.php">
                    <i class="fas fa-history"></i> 歷史記錄
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar"></i> 統計報表
                </a>
            </li>
            <li class="nav-item">
                <a href="export_form.php" class="nav-link">
                    <i class="fas fa-file-export"></i> 匯出報表
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link logout" href="../logout.php" style="color: #ea4335 !important; font-weight: bold; background-color: transparent !important;">
                    <i class="fas fa-sign-out-alt" style="color: #ea4335 !important;"></i> 登出
                </a>
            </li>
        </ul>
        <button onclick="toggleTheme()" class="theme-toggle" title="切換主題" style="font-size: 14px !important;">
            <i id="theme-icon" class="fas fa-sun" style="font-size: 14px !important;"></i>
            <span id="theme-text" style="font-size: 14px !important;">切換淺色模式</span>
        </button>
        <div class="user-info" style="display: flex; align-items: center; padding: 0 15px;">
            <i class="fas fa-user-circle" style="color: #4285f4 !important; font-size: 14px; margin-right: 10px;"></i>
            <div class="user-info-details" style="display: flex; align-items: center;">
                <span class="user-name" style="color: #ffffff !important; font-size: 14px; font-weight: bold; margin-right: 15px;"><?php echo htmlspecialchars($user['name']); ?></span>
                <span class="user-position" style="color: #34a853 !important; font-size: 11px; margin-right: 15px;">
                    <?php if (!empty($_SESSION['position_name'])): ?>
                        <i class="fas fa-id-badge" style="margin-right: 4px; font-size: 11px; color: #34a853 !important;"></i>
                        <?php echo htmlspecialchars($_SESSION['position_name']); ?>
                    <?php else: ?>
                        <i class="fas fa-user-tag" style="margin-right: 4px; font-size: 11px; color: #34a853 !important;"></i>
                        <?php echo $role_mapping[$_SESSION['role']] ?? '未設職位'; ?>
                    <?php endif; ?>
                </span>
                <span class="user-site" style="color: #aaaaaa !important; font-size: 14px;"><?php echo htmlspecialchars($user['site']); ?></span>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title"><i class="fas fa-history"></i> 人力需求派工歷史記錄</h1>
            <a href="export.php?start_date=<?php echo urlencode($startDate); ?>&end_date=<?php echo urlencode($endDate); ?>&site_id=<?php echo urlencode($siteId ?? ''); ?>&type=<?php echo urlencode($status); ?>" class="btn btn-primary">
                <i class="fas fa-file-export"></i> 匯出篩選結果
            </a>
        </div>

        <!-- 搜尋和篩選 -->
        <div class="dashboard-card">
            <div class="card-header">
                <h2><i class="fas fa-search"></i> 搜尋與篩選</h2>
            </div>
            <div class="card-body">
                <form method="GET" action="history.php" class="row" id="searchForm">
                    <div class="col-md-3 mb-3">
                        <label for="search">關鍵字搜尋</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                            </div>
                            <input type="text" class="form-control" id="search" name="search" placeholder="搜尋編號、工地名稱..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="start_date">開始日期</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            </div>
                            <input type="text" class="form-control datepicker" id="start_date" name="start_date" placeholder="開始日期" value="<?php echo htmlspecialchars($startDate); ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="end_date">結束日期</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                            </div>
                            <input type="text" class="form-control datepicker" id="end_date" name="end_date" placeholder="結束日期" value="<?php echo htmlspecialchars($endDate); ?>" readonly>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="site_id">工地篩選</label>
                        <select class="custom-select" id="site_id" name="site_id">
                            <option value="">所有工地</option>
                            <?php foreach ($sites as $site): ?>
                                <option value="<?php echo $site['id']; ?>" <?php if ($siteId == $site['id']) echo 'selected'; ?>>
                                    <?php echo htmlspecialchars($site['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="status">需求狀態</label>
                        <select class="custom-select" id="status" name="status">
                            <option value="all" <?php if ($status === 'all') echo 'selected'; ?>>所有狀態</option>
                            <option value="pending" <?php if ($status === 'pending') echo 'selected'; ?>>待審核</option>
                            <option value="approved" <?php if ($status === 'approved') echo 'selected'; ?>>已核准</option>
                            <option value="rejected" <?php if ($status === 'rejected') echo 'selected'; ?>>已拒絕</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary" id="searchButton">
                            <i class="fas fa-search mr-1"></i> 搜尋
                        </button>
                        <a href="history.php" class="btn btn-outline-secondary ml-2" id="resetButton">
                            <i class="fas fa-redo mr-1"></i> 重置
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 需求列表 -->
        <div class="dashboard-card">
            <div class="card-header">
                <h2><i class="fas fa-list"></i> 需求列表</h2>
                <span class="badge badge-pending"><?php echo $totalItems; ?> 筆</span>
            </div>
            <div class="card-body">
                <?php if (empty($requests)): ?>
                <div class="empty-state">
                    <i class="fas fa-clipboard-list"></i>
                    <p>無符合條件的記錄</p>
                </div>
                <?php else: ?>
                <!-- 批量操作工具欄 -->
                <?php if ($_SESSION['role'] == 'admin'): ?>
                <div class="batch-operations" style="margin-bottom: 15px; padding: 10px; background-color: var(--bg-secondary); border-radius: 5px; border: 1px solid var(--border-color);">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <label style="margin-right: 10px;">
                                <input type="checkbox" id="selectAll" style="margin-right: 5px;"> 全選
                            </label>
                            <span id="selectedCount" style="color: var(--text-secondary); font-size: 0.9rem;">已選擇 0 筆記錄</span>
                        </div>
                        <div>
                            <button type="button" id="batchDeleteBtn" class="btn btn-sm" style="background-color: #F44336; color: white;" disabled>
                                <i class="fas fa-trash-alt"></i> 批量刪除
                            </button>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="document-table">
                        <thead>
                            <tr>
                                <?php if ($_SESSION['role'] == 'admin'): ?>
                                <th style="width: 40px;">選擇</th>
                                <?php endif; ?>
                                <th>編號</th>
                                <th>工地</th>
                                <th>申請人</th>
                                <th>工作日期</th>
                                <th>項目</th>
                                <th>所需人數</th>
                                <th>實際人數</th>
                                <th>狀態</th>
                                <th>申請時間</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($requests as $request): ?>
                                <tr>
                                    <?php if ($_SESSION['role'] == 'admin'): ?>
                                    <td>
                                        <input type="checkbox" class="record-checkbox" value="<?php echo $request['id']; ?>" data-site="<?php echo htmlspecialchars($request['site_name']); ?>" data-date="<?php echo $request['work_date']; ?>">
                                    </td>
                                    <?php endif; ?>
                                    <td><?php echo $request['id']; ?></td>
                                    <td><?php echo htmlspecialchars($request['site_name']); ?></td>
                                    <td><?php echo htmlspecialchars($request['applicant_name']); ?></td>
                                    <td><?php echo $request['work_date']; ?></td>
                                    <td><?php echo $request['item_count']; ?> 項</td>
                                    <td><?php echo $request['total_workers_required'] ?: 0; ?> 人</td>
                                    <td>
                                        <?php if ($request['status'] === 'approved'): ?>
                                            <?php if ($request['recorded_items'] > 0): ?>
                                                <?php echo $request['total_actual_workers'] ?: 0; ?> 人
                                                <small class="text-muted">(<?php echo $request['recorded_items']; ?>/<?php echo $request['item_count']; ?> 項已記錄)</small>
                                            <?php else: ?>
                                                <span class="text-muted">尚未記錄</span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="status-indicator <?php echo $request['status']; ?>"></span>
                                        <?php echo translateStatus($request['status']); ?>
                                    </td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="view_request.php?id=<?php echo $request['id']; ?>" class="btn btn-primary btn-sm">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                            <?php if ($_SESSION['role'] == 'admin'): ?>
                                            <a href="delete_history.php?id=<?php echo $request['id']; ?>" class="btn btn-sm" style="background-color: #F44336; color: white;" onclick="return confirm('確定要刪除此記錄嗎？此操作無法撤銷！');">
                                                <i class="fas fa-trash-alt"></i> 刪除
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>

                <!-- 分頁與每頁顯示數量選擇器 -->
                <div class="pagination-container" style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                    <!-- 空白區域，用於平衡布局 -->
                    <div style="flex: 1;"></div>

                    <!-- 顯示記錄總數和當前頁 - 置中 -->
                    <div class="pagination-info" style="flex: 2; text-align: center; margin: 10px 0; font-weight: 500; padding: 6px 12px; border-radius: 4px; background-color: var(--bg-secondary); border: 1px solid var(--border-color);">
                        顯示 <span style="color: var(--primary-color); font-weight: bold;"><?php echo min(($page - 1) * $perPage + 1, $totalItems); ?> - <?php echo min($page * $perPage, $totalItems); ?></span> 筆，共 <span style="color: var(--primary-color); font-weight: bold;"><?php echo $totalItems; ?></span> 筆記錄
                    </div>

                    <!-- 每頁顯示數量選擇器 - 放在右下角，使用下拉選擇方式 -->
                    <div class="per-page-selector" style="flex: 1; text-align: right; margin: 10px 0;">
                        <span style="margin-right: 5px;">每頁顯示：</span>
                        <select class="form-control custom-select" style="display: inline-block; width: auto; background-color: var(--card-bg); color: var(--text-color); border: 1px solid var(--border-color);" onchange="window.location.href=this.value">
                            <?php foreach ($allowedPerPage as $option): ?>
                                <option value="?page=1&per_page=<?php echo $option; ?>&search=<?php echo urlencode($search); ?>&start_date=<?php echo urlencode($startDate); ?>&end_date=<?php echo urlencode($endDate); ?>&site_id=<?php echo $siteId; ?>&status=<?php echo $status; ?>"
                                        <?php if ($perPage == $option) echo 'selected'; ?>>
                                    <?php echo $option; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
    <script>
        $(document).ready(function() {
            // 處理搜尋表單提交
            $('#searchForm').on('submit', function(e) {
                // 驗證日期範圍
                const startDate = $('#start_date').val();
                const endDate = $('#end_date').val();

                if (startDate && endDate) {
                    const start = new Date(startDate);
                    const end = new Date(endDate);

                    if (start > end) {
                        alert('開始日期不能晚於結束日期');
                        e.preventDefault();
                        return false;
                    }
                }

                // 顯示加載指示器
                $('body').append('<div id="loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;"><div style="background-color: white; padding: 20px; border-radius: 5px;"><i class="fas fa-spinner fa-spin"></i> 搜尋中...</div></div>');

                // 確保表單提交
                console.log('表單提交中...');
                return true;
            });

            // 確保搜尋按鈕點擊時提交表單
            $('#searchButton').on('click', function() {
                $('#searchForm').submit();
            });

            // 強制設置用戶信息字體大小
            const userIconForce = document.querySelector('.user-info i.fas.fa-user-circle');
            const userNameForce = document.querySelector('.user-info .user-name');
            const userPositionForce = document.querySelector('.user-info .user-position');
            const userSiteForce = document.querySelector('.user-info .user-site');
            const positionIconsForce = document.querySelectorAll('.user-info .user-position i');
            const bellIconForce = document.querySelector('.notification-icon i.fas.fa-bell');

            if (userIconForce) userIconForce.style.setProperty('font-size', '14px', 'important');
            if (userNameForce) userNameForce.style.setProperty('font-size', '14px', 'important');
            if (userPositionForce) userPositionForce.style.setProperty('font-size', '11px', 'important');
            if (userSiteForce) userSiteForce.style.setProperty('font-size', '14px', 'important');
            if (bellIconForce) bellIconForce.style.setProperty('font-size', '14px', 'important');

            positionIconsForce.forEach(icon => {
                icon.style.setProperty('font-size', '11px', 'important');
            });

            // 初始化日期選擇器
            $('.datepicker').datepicker({
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true,
                orientation: 'top',
                container: 'body',
                zIndexOffset: 9999,
                clearBtn: true
            });

            // 確保日期選擇器正確顯示
            $('.datepicker').on('show', function(e) {
                // 確保日期選擇器在視窗內可見
                setTimeout(function() {
                    var datepicker = $('.datepicker-dropdown');
                    if (datepicker.length) {
                        // 獲取視窗高度和日期選擇器位置
                        var windowHeight = $(window).height();
                        var datepickerTop = datepicker.offset().top;
                        var datepickerHeight = datepicker.outerHeight();

                        // 如果日期選擇器超出視窗底部，調整位置
                        if (datepickerTop + datepickerHeight > windowHeight) {
                            datepicker.css({
                                'top': Math.max(10, windowHeight - datepickerHeight - 10) + 'px',
                                'z-index': '9999'
                            });
                        }
                    }
                }, 10);
            });

            // 強制設置導航欄水平顯示
            const navbarNav = document.getElementById('navbarNav');
            if (navbarNav) {
                navbarNav.style.setProperty('display', 'flex', 'important');
                navbarNav.style.setProperty('flex-direction', 'row', 'important');
                navbarNav.style.setProperty('justify-content', 'center', 'important');
                navbarNav.style.setProperty('align-items', 'center', 'important');
                navbarNav.style.setProperty('flex', '1', 'important');
            }

            // 設置導航項目樣式
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.style.setProperty('margin', '0 5px', 'important');
            });

            // 強制設置導航欄顏色
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                const currentTheme = localStorage.getItem('theme') || 'dark';
                if (currentTheme === 'light') {
                    navbar.style.setProperty('background-color', '#4361EE', 'important');

                    // 設置用戶信息顏色
                    const userIcon = document.querySelector('.user-info i');
                    const userName = document.querySelector('.user-name');
                    const userRole = document.querySelector('.user-role');

                    if (userIcon) userIcon.style.setProperty('color', '#4285f4', 'important');
                    if (userName) userName.style.setProperty('color', '#4285f4', 'important');
                    if (userRole) userRole.style.setProperty('color', '#4285f4', 'important');
                } else {
                    navbar.style.setProperty('background-color', '#1e1e1e', 'important');
                }
            }

            // 批量選擇功能
            const selectAllCheckbox = document.getElementById('selectAll');
            const recordCheckboxes = document.querySelectorAll('.record-checkbox');
            const selectedCountSpan = document.getElementById('selectedCount');
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');

            // 全選/取消全選
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    recordCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateSelectedCount();
                });
            }

            // 單個複選框變化
            recordCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateSelectedCount();

                    // 更新全選狀態
                    const checkedCount = document.querySelectorAll('.record-checkbox:checked').length;
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = checkedCount === recordCheckboxes.length;
                        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < recordCheckboxes.length;
                    }
                });
            });

            // 更新選中數量
            function updateSelectedCount() {
                const checkedCount = document.querySelectorAll('.record-checkbox:checked').length;
                if (selectedCountSpan) {
                    selectedCountSpan.textContent = `已選擇 ${checkedCount} 筆記錄`;
                }
                if (batchDeleteBtn) {
                    batchDeleteBtn.disabled = checkedCount === 0;
                }
            }



            // 批量刪除
            if (batchDeleteBtn) {
                batchDeleteBtn.addEventListener('click', function() {
                    const checkedBoxes = document.querySelectorAll('.record-checkbox:checked');
                    if (checkedBoxes.length === 0) {
                        alert('請選擇要刪除的記錄');
                        return;
                    }

                    // 收集選中的記錄信息
                    const selectedRecords = [];
                    checkedBoxes.forEach(checkbox => {
                        selectedRecords.push({
                            id: checkbox.value,
                            site: checkbox.dataset.site,
                            date: checkbox.dataset.date
                        });
                    });

                    // 顯示確認對話框
                    const recordList = selectedRecords.map(record =>
                        `編號: ${record.id}, 工地: ${record.site}, 日期: ${record.date}`
                    ).join('\n');

                    if (confirm(`確定要刪除以下 ${selectedRecords.length} 筆記錄嗎？此操作無法撤銷！\n\n${recordList}`)) {
                        // 創建表單並提交
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = 'batch_delete_history.php';

                        selectedRecords.forEach(record => {
                            const input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = 'record_ids[]';
                            input.value = record.id;
                            form.appendChild(input);
                        });

                        document.body.appendChild(form);
                        form.submit();
                    }
                });
            }
        });

        // 主題切換函數 - 使用外部JS文件中的函數
        function toggleTheme() {
            const currentTheme = localStorage.getItem('theme') || 'dark';
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            localStorage.setItem('theme', newTheme);
            document.body.className = 'theme-' + newTheme;

            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');

            if (themeIcon && themeText) {
                if (newTheme === 'light') {
                    themeIcon.className = 'fas fa-moon';
                    themeText.textContent = '切換深色模式';
                } else {
                    themeIcon.className = 'fas fa-sun';
                    themeText.textContent = '切換淺色模式';
                }
            }

            // 更新導航欄顏色
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                if (newTheme === 'light') {
                    navbar.style.setProperty('background-color', '#4361EE', 'important');

                    // 設置用戶信息顏色為藍色
                    const userIcon = document.querySelector('.user-info i.fas.fa-user-circle');
                    const userName = document.querySelector('.user-info .user-name');
                    const userPosition = document.querySelector('.user-info .user-position');
                    const userSite = document.querySelector('.user-info .user-site');
                    const positionIcons = document.querySelectorAll('.user-info .user-position i');

                    if (userIcon) {
                        userIcon.style.setProperty('color', '#4285f4', 'important');
                        userIcon.style.setProperty('font-size', '14px', 'important');
                    }
                    if (userName) {
                        userName.style.setProperty('color', '#4285f4', 'important');
                        userName.style.setProperty('font-size', '14px', 'important');
                    }
                    if (userPosition) {
                        userPosition.style.setProperty('color', '#4285f4', 'important');
                        userPosition.style.setProperty('font-size', '11px', 'important');
                    }
                    if (userSite) {
                        userSite.style.setProperty('color', '#4285f4', 'important');
                        userSite.style.setProperty('font-size', '14px', 'important');
                    }

                    positionIcons.forEach(icon => {
                        icon.style.setProperty('color', '#4285f4', 'important');
                        icon.style.setProperty('font-size', '11px', 'important');
                    });
                } else {
                    navbar.style.setProperty('background-color', '#1e1e1e', 'important');
                }
            }
        }
    </script>
</body>
</html>