<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

session_start();

// 檢查用戶是否已登入
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// 初始化數據庫連接
require_once('../db_service.php');
$db = DBService::getInstance();
$pdo = $db->getPdo();

// 獲取用戶資料
function getUserData($userId, $pdo) {
    try {
        $stmt = $pdo->prepare("SELECT u.*, p.name as position_name
                              FROM users u
                              LEFT JOIN positions p ON u.position_id = p.id
                              WHERE u.id = ?");
        $stmt->execute([$userId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        error_log("Error fetching user data: " . $e->getMessage());
        return false;
    }
}

// 獲取通知
function getNotifications($db, $user_id) {
    // 未讀通知
    $unread = $db->fetchAll(
        "SELECT * FROM notifications WHERE user_id = ? AND `read` = 0 ORDER BY created_at DESC LIMIT 10",
        [$user_id]
    );

    // 所有通知
    $all = $db->fetchAll(
        "SELECT * FROM notifications WHERE user_id = ? ORDER BY created_at DESC LIMIT 10",
        [$user_id]
    );

    return [
        'unread' => $unread,
        'all' => $all,
        'count' => count($unread)
    ];
}

// 映射表
$role_mapping = [
    'admin' => '系統管理者',
    'site_supervisor' => '工地主管',
    'hq_supervisor' => '總公司主管',
    'employee' => '員工'
];

// 獲取當前用戶資料
$user = getUserData($_SESSION['user_id'], $pdo);
if (!$user) {
    $_SESSION['error'] = "無法獲取用戶資料";
    header('Location: index.php');
    exit;
}

// 檢查用戶權限（總公司用戶、管理員和工地主管可以訪問）
$is_admin = $user['role'] === 'admin';
$is_site_supervisor = $user['role'] === 'site_supervisor';
$is_headquarters = $user['site'] === '總公司';

if (!$is_admin && !$is_site_supervisor && !$is_headquarters) {
    header('Location: index.php');
    exit;
}

// 獲取用戶完整信息
$fullname = $user['name'];
$site = $user['site'];
$role = $user['role'];

// 獲取工地列表
$sites = [];
try {
    // 根據用戶工地決定顯示哪些工地
    $user_site = $user['site'];

    if ($user_site === '總公司') {
        // 總公司用戶可以看到所有工地
        $stmt = $pdo->prepare("SELECT id, name FROM sites ORDER BY name");
        $stmt->execute();
    } else {
        // 其他用戶只能看到自己工地
        $stmt = $pdo->prepare("SELECT id, name FROM sites WHERE name = ? ORDER BY name");
        $stmt->execute([$user_site]);
    }

    $sites = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching sites: " . $e->getMessage());
}

// 設置默認值
$current_year = date('Y');
$current_month = date('n');
$years = range($current_year - 5, $current_year + 1);
$months = [
    1 => '1月', 2 => '2月', 3 => '3月', 4 => '4月',
    5 => '5月', 6 => '6月', 7 => '7月', 8 => '8月',
    9 => '9月', 10 => '10月', 11 => '11月', 12 => '12月'
];

// 設置默認日期範圍
$default_start_date = date('Y-m-01'); // 當月第一天
$default_end_date = date('Y-m-t'); // 當月最後一天

// 獲取工人價格設定
$price_settings = $db->fetchOne("SELECT * FROM worker_price_settings ORDER BY id DESC LIMIT 1");
if (!$price_settings) {
    // 如果沒有設定，創建默認設定
    $db->execute("INSERT INTO worker_price_settings (regular_price, holiday_price) VALUES (1500, 2000)");
    $price_settings = [
        'regular_price' => 1500,
        'holiday_price' => 2000
    ];
}

// 初始化消息
$error_message = '';
$success_message = '';

// 檢查SESSION中是否有錯誤或成功消息
if (isset($_SESSION['error'])) {
    $error_message = $_SESSION['error'];
    unset($_SESSION['error']);
}

if (isset($_SESSION['success'])) {
    $success_message = $_SESSION['success'];
    unset($_SESSION['success']);
}

// 處理表單提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 檢查是否更新價格設定
    if (isset($_POST['update_price'])) {
        $regular_price = isset($_POST['regular_price']) ? floatval($_POST['regular_price']) : 1500;
        $holiday_price = isset($_POST['holiday_price']) ? floatval($_POST['holiday_price']) : 2000;

        // 更新價格設定
        $db->execute(
            "UPDATE worker_price_settings SET regular_price = ?, holiday_price = ?, updated_at = NOW() WHERE id = ?",
            [$regular_price, $holiday_price, $price_settings['id']]
        );

        // 重新獲取價格設定
        $price_settings = $db->fetchOne("SELECT * FROM worker_price_settings WHERE id = ?", [$price_settings['id']]);

        // 顯示成功訊息
        $_SESSION['success'] = "工人工作價格設定已更新";
        header('Location: export_form.php');
        exit;
    } else {
        // 檢查匯出模式
        $export_mode = isset($_POST['export_mode']) ? $_POST['export_mode'] : 'month';
        $site_id = isset($_POST['site_id']) ? intval($_POST['site_id']) : 0;
        $regular_price = isset($_POST['regular_price']) ? floatval($_POST['regular_price']) : $price_settings['regular_price'];
        $holiday_price = isset($_POST['holiday_price']) ? floatval($_POST['holiday_price']) : $price_settings['holiday_price'];

        if ($export_mode === 'month') {
            // 按月份匯出
            $year = isset($_POST['year']) ? intval($_POST['year']) : $current_year;
            $month = isset($_POST['month']) ? intval($_POST['month']) : $current_month;

            // 計算所選月份的開始和結束日期
            $start_date = sprintf('%04d-%02d-01', $year, $month);
            $end_date = date('Y-m-t', strtotime($start_date));
        } else {
            // 按自定義日期範圍匯出
            $start_date = isset($_POST['custom_start_date']) ? $_POST['custom_start_date'] : $default_start_date;
            $end_date = isset($_POST['custom_end_date']) ? $_POST['custom_end_date'] : $default_end_date;

            // 驗證日期格式
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $start_date)) {
                $start_date = $default_start_date;
            }
            if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $end_date)) {
                $end_date = $default_end_date;
            }

            // 確保開始日期不晚於結束日期
            if (strtotime($start_date) > strtotime($end_date)) {
                $temp = $start_date;
                $start_date = $end_date;
                $end_date = $temp;
            }
        }

        // 重定向到導出頁面
        header("Location: export.php?start_date=$start_date&end_date=$end_date&site_id=$site_id&regular_price=$regular_price&holiday_price=$holiday_price");
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>導出人力需求報表 - 良有營造股份有限公司電子簽核系統</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="../dark-theme.css">
    <link rel="stylesheet" href="../light-theme.css">
    <link rel="stylesheet" href="../responsive.css">
    <link rel="stylesheet" href="../navbar.css">
    <link rel="stylesheet" href="user-info-style.css">
    <link rel="stylesheet" href="user-info-color.css">
    <link rel="stylesheet" href="force-animation.css">
    <link rel="stylesheet" href="compact-user-info.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css">
    <!-- 使用簡化版主題切換器 -->
    <script src="theme-switcher-custom.js" defer></script>
    <!-- 強制設置用戶信息顏色 -->
    <script src="force-user-info-color.js" defer></script>
    <!-- 強制設置用戶信息字體大小 -->
    <style>
        /* 使用最高優先級的選擇器強制設置字體大小 */
        .user-info i.fas.fa-user-circle,
        .user-info i.fas.fa-user-circle[style],
        .user-info > i.fas.fa-user-circle,
        .user-info > i.fas.fa-user-circle[style] {
            font-size: 14px !important;
        }

        .user-name,
        .user-name[style],
        .user-info .user-name,
        .user-info .user-name[style],
        .user-info > .user-info-details > .user-name,
        .user-info > .user-info-details > .user-name[style] {
            font-size: 14px !important;
        }

        .user-position,
        .user-position[style],
        .user-info .user-position,
        .user-info .user-position[style],
        .user-info > .user-info-details > .user-position,
        .user-info > .user-info-details > .user-position[style] {
            font-size: 11px !important;
        }

        .user-position i,
        .user-position i[style],
        .user-info .user-position i,
        .user-info .user-position i[style],
        .user-info > .user-info-details > .user-position i,
        .user-info > .user-info-details > .user-position i[style] {
            font-size: 11px !important;
        }

        .user-site,
        .user-site[style],
        .user-info .user-site,
        .user-info .user-site[style],
        .user-info > .user-info-details > .user-site,
        .user-info > .user-info-details > .user-site[style] {
            font-size: 14px !important;
        }

        .notification-icon i.fas.fa-bell,
        .notification-icon i.fas.fa-bell[style] {
            font-size: 14px !important;
        }

        /* 日期選擇器樣式優化 */
        .datepicker-dropdown {
            margin-top: 0;
            z-index: 10000 !important;
        }

        .input-group-text {
            background-color: var(--bg-tertiary);
            border-color: var(--border-color);
            color: var(--text-primary);
        }

        .datepicker table tr td.active,
        .datepicker table tr td.active:hover,
        .datepicker table tr td.active.disabled,
        .datepicker table tr td.active.disabled:hover {
            background-color: var(--primary-color) !important;
            background-image: none !important;
        }

        .datepicker table tr td.today,
        .datepicker table tr td.today:hover,
        .datepicker table tr td.today.disabled,
        .datepicker table tr td.today.disabled:hover {
            background-color: var(--warning-color) !important;
            background-image: none !important;
            color: #000 !important;
        }

        /* 深色模式下的日期選擇器樣式 */
        body.theme-dark .datepicker-dropdown {
            background-color: #333 !important;
            border-color: #444 !important;
        }

        body.theme-dark .datepicker table tr td,
        body.theme-dark .datepicker table tr th {
            color: #fff !important;
        }

        body.theme-dark .datepicker table tr td.day:hover {
            background-color: #444 !important;
        }

        body.theme-dark .datepicker table tr td.old,
        body.theme-dark .datepicker table tr td.new {
            color: #888 !important;
        }
    </style>
    <style>
        :root {
            --primary-color: #1a73e8;
            --primary-hover: #1557b0;
            --secondary-color: #34a853;
            --secondary-hover: #2d9249;
            --danger-color: #ea4335;
            --danger-hover: #c5221f;
            --warning-color: #fbbc05;
            --warning-hover: #f29900;
            --info-color: #2196F3;

            --text-primary: #E0E0E0;
            --text-secondary: #AAAAAA;
            --text-muted: #888888;
            --text-color: #ffffff;

            --dark-bg: #121212;
            --bg-primary: #121212;
            --bg-secondary: #1E1E1E;
            --bg-tertiary: #252525;
            --bg-input: #333333;

            --border-color: #444444;
            --card-bg: #1E1E1E;
            --card-header: #252525;
            --table-header: #2c2c2c;
            --table-row-hover: #2a2a2a;
            --hover-bg: rgba(255, 255, 255, 0.05);

            --shadow-color: rgba(0, 0, 0, 0.4);
            --navbar-bg: #1e1e1e;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);

            --success-badge: rgba(52, 168, 83, 0.15);
            --warning-badge: rgba(251, 188, 5, 0.15);
            --danger-badge: rgba(234, 67, 53, 0.15);
            --info-badge: rgba(26, 115, 232, 0.15);
        }

        /* 淺色模式樣式 */
        body.theme-light {
            --primary-color: #1a73e8;
            --success-color: #34a853;
            --warning-color: #fbbc05;
            --danger-color: #ea4335;
            --info-color: #4285f4;

            --text-primary: #333333;
            --text-secondary: #666666;
            --text-muted: #888888;

            --bg-primary: #f5f5f5;
            --bg-secondary: #ffffff;
            --bg-tertiary: #f0f0f0;
            --bg-input: #ffffff;

            --border-color: #dddddd;
            --card-bg: #ffffff;
            --card-header: #f0f0f0;
            --table-header: #f0f0f0;
            --hover-bg: rgba(0, 0, 0, 0.05);

            --shadow-color: rgba(0, 0, 0, 0.1);
            --navbar-bg: #4361EE;
            --navbar-text: #ffffff;
            --navbar-hover: rgba(255, 255, 255, 0.1);
        }

        /* 為淺色模式添加特定樣式，確保導覽列中的選項顯示白色 */
        body.theme-light .navbar .nav-link,
        body.theme-light .navbar .nav-link i {
            color: var(--navbar-text) !important;
        }

        body.theme-light .navbar .nav-item:hover .nav-link,
        body.theme-light .navbar .nav-item:hover .nav-link i {
            color: var(--navbar-text) !important;
        }

        /* 為淺色模式下的下拉式選單添加特定樣式 */
        body.theme-light .dropdown-content {
            background-color: #ffffff;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            border-radius: 0 0 8px 8px;
        }

        body.theme-light .dropdown-content a {
            color: #333333 !important;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        body.theme-light .dropdown-content a:last-child {
            border-bottom: none;
        }

        body.theme-light .dropdown-content a i {
            color: #4361EE !important;
            margin-right: 8px;
            width: 20px;
            text-align: center;
        }

        body.theme-light .dropdown-content a:hover {
            background-color: #f0f0f0;
            padding-left: 20px;
        }

        body.theme-light .dropdown-content a:hover i {
            color: #1a73e8 !important;
        }

        /* 帳號申請管理待處理標誌在淺色模式下的樣式 */
        body.theme-light .pending-applications-badge {
            background-color: #ea4335;
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 帳號申請管理選項的顏色加強 */
        body.theme-light .dropdown-content a:nth-last-child(1) {
            font-weight: 500;
        }

        /* 為登出按鈕添加紅色樣式（淺色模式） */
        body.theme-light .navbar .nav-link.logout,
        body.theme-light .navbar .nav-link.logout i {
            color: #ea4335 !important; /* 使用危險色作為登出按鈕顏色 */
        }

        body.theme-light .navbar .nav-link.logout:hover,
        body.theme-light .navbar .nav-link.logout:hover i {
            color: #c5221f !important; /* 使用危險色的懸停顏色 */
        }

        /* 為登出按鈕添加紅色樣式（深色模式） */
        body.theme-dark .navbar .nav-link.logout,
        body.theme-dark .navbar .nav-link.logout i,
        .navbar .nav-link.logout,
        .navbar .nav-link.logout i {
            color: #ea4335 !important; /* 使用危險色作為登出按鈕顏色 */
        }

        body.theme-dark .navbar .nav-link.logout:hover,
        body.theme-dark .navbar .nav-link.logout:hover i,
        .navbar .nav-link.logout:hover,
        .navbar .nav-link.logout:hover i {
            color: #c5221f !important; /* 使用危險色的懸停顏色 */
        }

        body {
            font-family: 'Microsoft JhengHei', 'PingFang TC', sans-serif;
            background-color: var(--bg-primary);
            color: var(--text-primary);
            margin: 0;
            padding: 0;
            line-height: 1.6;
        }

        .navbar {
            background-color: var(--navbar-bg);
            padding: 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            padding: 5px 15px;
            height: 45px;
        }

        .navbar-brand img {
            height: 35px;
            margin-right: 10px;
            border-radius: 50%;
        }

        .navbar-brand h1 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            color: var(--navbar-text);
        }

        .navbar-nav {
            display: flex;
            list-style: none;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            display: flex;
            align-items: center;
            color: var(--navbar-text);
            text-decoration: none;
            padding: 12px 15px;
            transition: all 0.3s;
            height: 45px;
            box-sizing: border-box;
            font-size: 16px;
        }

        .nav-link:hover {
            background-color: var(--navbar-hover);
            color: var(--navbar-text);
        }

        .nav-link i {
            margin-right: 8px;
            font-size: 16px;
        }

        .nav-link.active {
            background-color: var(--primary-color);
        }

        .logout {
            color: var(--danger-color) !important;
            background-color: transparent !important; /* 確保背景色為透明 */
        }

        .logout:hover {
            background-color: rgba(234, 67, 53, 0.1) !important;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: var(--card-bg);
            min-width: 180px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
            border-radius: 0 0 4px 4px;
            z-index: 1;
        }

        .nav-item:hover .dropdown-content {
            display: block;
        }

        .dropdown-content a {
            color: var(--text-color);
            padding: 12px 15px;
            text-decoration: none;
            display: block;
            transition: all 0.2s;
            font-size: 16px;
        }

        .dropdown-content a:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }

        .pending-applications-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 10px;
            background-color: var(--danger-color);
            color: white;
            font-size: 10px;
            margin-left: 5px;
        }

        .container {
            max-width: 1200px;
            margin: 60px auto 20px;
            padding: 20px;
        }

        .card {
            background-color: var(--card-bg);
            border-radius: 8px;
            box-shadow: 0 4px 6px var(--shadow-color);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--card-header);
            padding: 15px 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-header h2 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            background-color: var(--bg-input);
            color: var(--text-primary);
            font-size: 16px;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin: -10px;
        }

        .form-col {
            flex: 1;
            padding: 10px;
            min-width: 200px;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background-color: var(--primary-color);
            color: white;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: var(--primary-hover);
        }

        .btn-secondary {
            background-color: #6c757d;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .text-center {
            text-align: center;
        }

        .mt-4 {
            margin-top: 20px;
        }

        /* 警告和成功消息樣式 */
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 4px;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        /* 深色模式下的警告和成功消息樣式 */
        body.theme-dark .alert-success {
            color: #d4edda;
            background-color: rgba(21, 87, 36, 0.7);
            border-color: #155724;
        }

        body.theme-dark .alert-danger {
            color: #f8d7da;
            background-color: rgba(114, 28, 36, 0.7);
            border-color: #721c24;
        }

        /* 通知樣式 */
        .user-info {
            display: flex;
            align-items: center;
            padding: 0 15px;
            position: relative;
            color: var(--navbar-text);
            height: 45px;
            box-sizing: border-box;
        }

        .user-info i {
            font-size: 24px;
            margin-right: 10px;
            color: #4285f4 !important;
        }

        .user-info-details {
            display: flex;
            flex-direction: column;
            margin-right: 15px;
        }

        .user-name {
            font-weight: 500;
            font-size: 14px;
            color: #4285f4 !important;
        }

        .user-position, .user-site {
            font-size: 14px;
            color: #4285f4 !important;
        }

        .notification-icon {
            margin-left: 15px;
            position: relative;
            cursor: pointer;
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-dropdown {
            display: none;
            position: absolute;
            right: 0;
            top: 45px;
            width: 300px;
            background-color: var(--card-bg);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
            border-radius: 0 0 4px 4px;
            z-index: 1000;
            max-height: 400px;
            overflow-y: auto;
        }

        .notification-item {
            padding: 10px 15px;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.2s;
        }

        .notification-item:last-child {
            border-bottom: none;
        }

        .notification-item.unread {
            background-color: rgba(26, 115, 232, 0.1);
        }

        .notification-time {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 5px;
        }

        /* 主題切換按鈕 */
        .theme-toggle {
            background: none;
            border: none;
            color: var(--navbar-text);
            cursor: pointer;
            padding: 0 15px;
            height: 45px;
            display: flex;
            align-items: center;
        }

        .theme-toggle i {
            margin-right: 8px;
        }

        #theme-text {
            display: inline;
        }

        /* 響應式設計 */
        @media (max-width: 768px) {
            .form-col {
                flex: 100%;
            }

            .navbar {
                flex-wrap: wrap;
            }

            .navbar-brand {
                width: 100%;
                justify-content: space-between;
            }

            .navbar-toggler {
                display: block;
                background: none;
                border: none;
                color: var(--navbar-text);
                font-size: 20px;
                cursor: pointer;
            }

            .navbar-nav {
                flex-direction: column;
                width: 100%;
                display: none;
            }

            .navbar-nav.active {
                display: flex;
            }

            .nav-item {
                width: 100%;
            }

            .dropdown-content {
                position: static;
                box-shadow: none;
                width: 100%;
                border-radius: 0;
            }

            .dropdown-content a {
                padding-left: 30px;
            }

            .user-info {
                width: 100%;
                border-left: none;
                border-top: 1px solid var(--border-color);
                padding: 10px 15px;
                justify-content: space-between;
            }

            .notification-dropdown {
                width: 100%;
                right: 0;
                left: 0;
            }
        }
    </style>
</head>
<body>
    <!-- 導航欄 -->
    <nav class="navbar" data-theme-element="navbar">
        <div class="navbar-brand">
            <img src="https://paper.lybuild.com.tw/images/logo.png" alt="良有營造標誌">
            <h1>良有營造電子簽核系統</h1>
            <button class="navbar-toggler" id="navbarToggler">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <ul class="navbar-nav" id="navbarNav">
            <li class="nav-item">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-tachometer-alt"></i> 簽核總覽
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-users-cog"></i> 人力需求派工
                </a>
            </li>
            <li class="nav-item">
                <a href="history.php" class="nav-link">
                    <i class="fas fa-history"></i> 歷史記錄
                </a>
            </li>
            <li class="nav-item">
                <a href="reports.php" class="nav-link">
                    <i class="fas fa-chart-bar"></i> 統計報表
                </a>
            </li>
            <li class="nav-item">
                <a href="export_form.php" class="nav-link active">
                    <i class="fas fa-file-export"></i> 匯出報表
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link logout" href="../logout.php" style="color: #ea4335 !important; font-weight: bold; background-color: transparent !important;">
                    <i class="fas fa-sign-out-alt" style="color: #ea4335 !important;"></i> 登出
                </a>
            </li>
        </ul>
        <button onclick="toggleTheme()" class="theme-toggle" title="切換主題" style="font-size: 14px !important;">
            <i id="theme-icon" class="fas fa-sun" style="font-size: 14px !important;"></i>
            <span id="theme-text" style="font-size: 14px !important;">切換淺色模式</span>
        </button>
        <div class="user-info" style="display: flex; align-items: center; padding: 0 15px;">
            <i class="fas fa-user-circle" style="color: #4285f4 !important; font-size: 14px; margin-right: 10px;"></i>
            <div class="user-info-details" style="display: flex; align-items: center;">
                <span class="user-name" style="color: #ffffff !important; font-size: 14px; font-weight: bold; margin-right: 15px;"><?php echo htmlspecialchars($fullname); ?></span>
                <span class="user-position" style="color: #34a853 !important; font-size: 11px; margin-right: 15px;">
                    <?php if (!empty($user['position_name'])): ?>
                        <i class="fas fa-id-badge" style="margin-right: 4px; font-size: 11px; color: #34a853 !important;"></i>
                        <?php echo htmlspecialchars($user['position_name']); ?>
                    <?php else: ?>
                        <i class="fas fa-user-tag" style="margin-right: 4px; font-size: 11px; color: #34a853 !important;"></i>
                        <?php echo isset($role) && isset($role_mapping[$role]) ? $role_mapping[$role] : '未設職位'; ?>
                    <?php endif; ?>
                </span>
                <span class="user-site" style="color: #aaaaaa !important; font-size: 14px;"><?php echo htmlspecialchars($site); ?></span>
            </div>
            <div class="notification-icon" onclick="toggleNotifications()">
                <i class="fas fa-bell" style="color: #4285f4 !important; font-size: 14px !important;"></i>
                <?php
                    // 獲取通知
                    $notificationsData = getNotifications($db, $_SESSION['user_id']);
                    $all_notifications = $notificationsData['all'];
                    $notifications = $notificationsData['unread'];
                    $notification_count = $notificationsData['count'];
                ?>
                <span class="notification-badge" id="notification-badge" <?php echo $notification_count > 0 ? '' : 'style="display:none;"'; ?>>
                    <?php echo $notification_count; ?>
                </span>

                <div class="notification-dropdown" id="notificationDropdown">
                    <div class="notification-header" style="padding: 10px 15px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                        <h3 style="margin: 0; font-size: 16px;">通知訊息</h3>
                        <?php if (count($all_notifications) > 0): ?>
                        <button id="mark-read-btn" class="btn btn-sm" style="padding: 2px 8px; font-size: 12px; background-color: var(--primary-color); color: white; border: none; border-radius: 4px; cursor: pointer;" onclick="markAllAsRead(event)">
                            <i class="fas fa-check"></i> 全部已讀
                        </button>
                        <?php endif; ?>
                    </div>
                    <div id="notifications-list">
                        <?php if (count($all_notifications) > 0): ?>
                            <?php foreach ($all_notifications as $notification): ?>
                            <div class="notification-item <?php echo $notification['read'] ? 'read' : 'unread'; ?>">
                                <div><?php echo htmlspecialchars($notification['message']); ?></div>
                                <div class="notification-time"><?php echo date('Y-m-d H:i', strtotime($notification['created_at'])); ?></div>
                            </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="notification-item">
                                <div>目前沒有新通知</div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="dashboard-card">
            <div class="card-header">
                <h2><i class="fas fa-file-export"></i> 導出人力需求報表</h2>
            </div>
            <div class="card-body">
                <form method="post" action="export_form.php">
                    <!-- 匯出模式選擇 -->
                    <div class="form-group">
                        <label>匯出模式</label>
                        <div style="display: flex; margin-bottom: 15px;">
                            <div style="margin-right: 20px;">
                                <input type="radio" id="mode_month" name="export_mode" value="month" checked onchange="toggleExportMode()">
                                <label for="mode_month" style="display: inline; margin-left: 5px;">按月份匯出</label>
                            </div>
                            <div>
                                <input type="radio" id="mode_custom" name="export_mode" value="custom" onchange="toggleExportMode()">
                                <label for="mode_custom" style="display: inline; margin-left: 5px;">自定義日期範圍</label>
                            </div>
                        </div>
                    </div>

                    <!-- 按月份匯出選項 -->
                    <div id="month_options" class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="year">年份</label>
                                <select class="form-control" id="year" name="year">
                                    <?php foreach ($years as $year): ?>
                                        <option value="<?php echo $year; ?>" <?php echo $year == $current_year ? 'selected' : ''; ?>>
                                            <?php echo $year; ?>年
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="month">月份</label>
                                <select class="form-control" id="month" name="month">
                                    <?php foreach ($months as $num => $name): ?>
                                        <option value="<?php echo $num; ?>" <?php echo $num == $current_month ? 'selected' : ''; ?>>
                                            <?php echo $name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 自定義日期範圍選項 -->
                    <div id="custom_options" class="form-row" style="display: none; position: relative;">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="custom_start_date">開始日期</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" id="custom_start_date" name="custom_start_date" placeholder="YYYY-MM-DD" value="<?php echo $default_start_date; ?>" readonly>
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="cursor: pointer;" onclick="$('#custom_start_date').datepicker('show');">
                                            <i class="fas fa-calendar-alt"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="custom_end_date">結束日期</label>
                                <div class="input-group">
                                    <input type="text" class="form-control datepicker" id="custom_end_date" name="custom_end_date" placeholder="YYYY-MM-DD" value="<?php echo $default_end_date; ?>" readonly>
                                    <div class="input-group-append">
                                        <span class="input-group-text" style="cursor: pointer;" onclick="$('#custom_end_date').datepicker('show');">
                                            <i class="fas fa-calendar-alt"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 工地選擇 -->
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="site_id">工地</label>
                                <select class="form-control" id="site_id" name="site_id">
                                    <option value="0">全部工地</option>
                                    <?php foreach ($sites as $site): ?>
                                        <option value="<?php echo $site['id']; ?>">
                                            <?php echo htmlspecialchars($site['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 工人工作價格設定 -->
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="regular_price">平日工作價格 (每人/日)</label>
                                <input type="number" class="form-control" id="regular_price" name="regular_price" value="<?php echo $price_settings['regular_price']; ?>" min="0" step="1">
                                <small class="form-text text-muted">適用於星期一至星期日的非國定假日</small>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="holiday_price">假日工作價格 (每人/日)</label>
                                <input type="number" class="form-control" id="holiday_price" name="holiday_price" value="<?php echo $price_settings['holiday_price']; ?>" min="0" step="1">
                                <small class="form-text text-muted">僅適用於星期一至星期五遇到國定假日</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group text-center mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-download"></i> 導出報表
                        </button>
                        <button type="submit" name="update_price" class="btn btn-success">
                            <i class="fas fa-save"></i> 更新價格設定
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 匯入CSV功能 -->
        <div class="dashboard-card" id="import-csv">
            <div class="card-header">
                <h2><i class="fas fa-file-import"></i> 匯入CSV數據</h2>
            </div>
            <div class="card-body">
                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger">
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success">
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <form method="post" action="import_process.php" enctype="multipart/form-data">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="import_site_id">工地 <span style="color: red;">*</span></label>
                                <select class="form-control" id="import_site_id" name="site_id" required>
                                    <option value="">請選擇工地</option>
                                    <?php foreach ($sites as $site): ?>
                                        <option value="<?php echo $site['id']; ?>">
                                            <?php echo htmlspecialchars($site['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="import_month">月份 <span style="color: red;">*</span></label>
                                <input type="month" class="form-control" id="import_month" name="month" required value="<?php echo date('Y-m'); ?>">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="csv_file">CSV文件 <span style="color: red;">*</span></label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <small class="form-text text-muted">請上傳符合格式的CSV文件，文件格式請參考說明</small>
                    </div>

                    <div class="form-group">
                        <div class="card" style="background-color: var(--bg-tertiary); margin-bottom: 20px;">
                            <div class="card-body">
                                <h5 style="font-size: 16px; margin-bottom: 10px;"><i class="fas fa-info-circle"></i> CSV文件格式說明</h5>
                                <ol style="padding-left: 20px; margin-bottom: 0;">
                                    <li>CSV文件需包含標題行（前三行）</li>
                                    <li>第一列為日期（1-31）</li>
                                    <li>第二列為總工人數量</li>
                                    <li>第三列為工作描述</li>
                                    <li>第四列開始為各公司的工人數量（良有營造、冠凌、國富、祈勝、亞東、鳳勝）</li>
                                    <li>系統會自動跳過沒有工人的日期</li>
                                </ol>
                            </div>
                        </div>
                    </div>

                    <div class="form-group text-center mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> 匯入數據
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
    <script>
    // 確保在DOM載入後立即執行
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化日期選擇器
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true,
            orientation: 'top',
            container: '#custom_options',
            zIndexOffset: 9999,
            clearBtn: true
        });

        // 強制設置用戶信息字體大小
        const userIconForce = document.querySelector('.user-info i.fas.fa-user-circle');
        const userNameForce = document.querySelector('.user-info .user-name');
        const userPositionForce = document.querySelector('.user-info .user-position');
        const userSiteForce = document.querySelector('.user-info .user-site');
        const positionIconsForce = document.querySelectorAll('.user-info .user-position i');
        const bellIconForce = document.querySelector('.notification-icon i.fas.fa-bell');

        if (userIconForce) userIconForce.style.setProperty('font-size', '14px', 'important');
        if (userNameForce) userNameForce.style.setProperty('font-size', '14px', 'important');
        if (userPositionForce) userPositionForce.style.setProperty('font-size', '11px', 'important');
        if (userSiteForce) userSiteForce.style.setProperty('font-size', '14px', 'important');
        if (bellIconForce) bellIconForce.style.setProperty('font-size', '14px', 'important');

        positionIconsForce.forEach(icon => {
            icon.style.setProperty('font-size', '11px', 'important');
        });

        // 立即強制設置導覽條顏色
        function forceNavbarColor() {
            const navbar = document.querySelector('.navbar');
            if (!navbar) return;

            // 檢查當前主題
            const currentTheme = localStorage.getItem('theme') || 'dark';

            // 強制設置內聯樣式（優先級最高）
            if (currentTheme === 'light') {
                navbar.style.setProperty('background-color', '#4361EE', 'important');
                console.log('設置淺色模式導覽條顏色: #4361EE');

                // 設置用戶信息顏色
                const userIcon = document.querySelector('.user-info i');
                const userName = document.querySelector('.user-name');
                const userPosition = document.querySelector('.user-position');
                const userSite = document.querySelector('.user-site');
                const positionIcons = document.querySelectorAll('.user-info .user-position i');

                if (userIcon) {
                    userIcon.style.setProperty('color', '#4285f4', 'important');
                    userIcon.style.setProperty('font-size', '14px', 'important');
                }
                if (userName) {
                    userName.style.setProperty('color', '#4285f4', 'important');
                    userName.style.setProperty('font-size', '14px', 'important');
                }
                if (userPosition) {
                    userPosition.style.setProperty('color', '#4285f4', 'important');
                    userPosition.style.setProperty('font-size', '11px', 'important');
                }
                if (userSite) {
                    userSite.style.setProperty('color', '#4285f4', 'important');
                    userSite.style.setProperty('font-size', '14px', 'important');
                }

                positionIcons.forEach(icon => {
                    icon.style.setProperty('color', '#4285f4', 'important');
                    icon.style.setProperty('font-size', '11px', 'important');
                });
            } else {
                navbar.style.setProperty('background-color', '#1e1e1e', 'important');
                console.log('設置深色模式導覽條顏色: #1e1e1e');

                // 深色模式下設置不同顏色
                const userIcon = document.querySelector('.user-info i.fas.fa-user-circle');
                const userName = document.querySelector('.user-info .user-name');
                const userPosition = document.querySelector('.user-info .user-position');
                const userSite = document.querySelector('.user-info .user-site');
                const positionIcons = document.querySelectorAll('.user-info .user-position i');
                const bellIcon = document.querySelector('.notification-icon i.fas.fa-bell');

                if (userIcon) {
                    userIcon.style.setProperty('color', '#4285f4', 'important');
                    userIcon.style.setProperty('font-size', '14px', 'important');
                }
                if (userName) {
                    userName.style.setProperty('color', '#ffffff', 'important');
                    userName.style.setProperty('font-size', '14px', 'important');
                }
                if (userPosition) {
                    userPosition.style.setProperty('color', '#34a853', 'important');
                    userPosition.style.setProperty('font-size', '11px', 'important');
                }
                if (userSite) {
                    userSite.style.setProperty('color', '#aaaaaa', 'important');
                    userSite.style.setProperty('font-size', '14px', 'important');
                }
                if (bellIcon) {
                    bellIcon.style.setProperty('color', '#4285f4', 'important');
                    bellIcon.style.setProperty('font-size', '14px', 'important');
                }

                positionIcons.forEach(icon => {
                    icon.style.setProperty('color', '#34a853', 'important');
                    icon.style.setProperty('font-size', '11px', 'important');
                });
            }
        }

        // 初始設置顏色
        forceNavbarColor();

        // 確保顏色設置生效
        setTimeout(forceNavbarColor, 100);
        setTimeout(forceNavbarColor, 500);

        // 監聽主題變化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class' && mutation.target === document.body) {
                    forceNavbarColor();
                    // 確保顏色設置生效
                    setTimeout(forceNavbarColor, 100);
                }
            });
        });

        // 監視body的class變化
        observer.observe(document.body, { attributes: true });

        // 響應式導覽列切換
        const navbarToggler = document.getElementById('navbarToggler');
        const navbarNav = document.getElementById('navbarNav');

        if (navbarToggler && navbarNav) {
            navbarToggler.addEventListener('click', function() {
                navbarNav.classList.toggle('active');
            });
        }
    });

    // 主題切換函數
    function toggleTheme() {
        const currentTheme = localStorage.getItem('theme') || 'dark';
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        localStorage.setItem('theme', newTheme);
        document.body.className = 'theme-' + newTheme;

        const themeIcon = document.getElementById('theme-icon');
        const themeText = document.getElementById('theme-text');

        if (themeIcon && themeText) {
            if (newTheme === 'light') {
                themeIcon.className = 'fas fa-moon';
                themeText.textContent = '切換深色模式';
            } else {
                themeIcon.className = 'fas fa-sun';
                themeText.textContent = '切換淺色模式';
            }
        }
    }

    // 切換匯出模式
    function toggleExportMode() {
        const monthMode = document.getElementById('mode_month').checked;
        const monthOptions = document.getElementById('month_options');
        const customOptions = document.getElementById('custom_options');

        if (monthMode) {
            monthOptions.style.display = 'flex';
            customOptions.style.display = 'none';
        } else {
            monthOptions.style.display = 'none';
            customOptions.style.display = 'flex';
        }
    }

    // 通知訊息切換
    function toggleNotifications() {
        const dropdown = document.getElementById('notificationDropdown');
        if (dropdown) {
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        }
    }

    // 將所有通知標記為已讀
    function markAllAsRead(event) {
        fetch('../mark_notifications_read.php', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新通知計數顯示
                const badge = document.getElementById('notification-badge');
                if (badge) {
                    badge.style.display = 'none';
                    badge.textContent = '0';
                }

                // 更新通知項目樣式
                const notifications = document.querySelectorAll('.notification-item.unread');
                notifications.forEach(item => {
                    item.classList.remove('unread');
                    item.classList.add('read');
                });

                // 如果關閉通知下拉選單，等待一段時間讓用戶看到效果
                setTimeout(() => {
                    toggleNotifications();
                }, 1000);
            } else {
                console.error('標記通知失敗:', data.message);
            }
        })
        .catch(error => {
            console.error('處理通知時發生錯誤:', error);
        });

        // 防止事件冒泡，避免觸發通知下拉選單的關閉
        if (event) event.stopPropagation();
    }
    </script>
</body>
</html>
